<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= esc($page_header) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('staff/farms') ?>">Farm Blocks</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Create</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('staff/farms') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle"></i> Please fix the following errors:</h6>
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus"></i> Create New Farm Block
                    </h6>
                </div>
                <div class="card-body">
                    <?= form_open('staff/farms', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        
                        <div class="row">
                            <!-- Farmer Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="farmer_id" class="form-label">Farmer <span class="text-danger">*</span></label>
                                <select class="form-select" id="farmer_id" name="farmer_id" required>
                                    <option value="">Select Farmer</option>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <option value="<?= $farmer['id'] ?>" <?= old('farmer_id') == $farmer['id'] ? 'selected' : '' ?>>
                                            <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?> 
                                            (<?= esc($farmer['farmer_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select a farmer.</div>
                            </div>

                            <!-- Crop Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="crop_id" class="form-label">Crop <span class="text-danger">*</span></label>
                                <select class="form-select" id="crop_id" name="crop_id" required>
                                    <option value="">Select Crop</option>
                                    <?php foreach ($crops as $crop): ?>
                                        <option value="<?= $crop['id'] ?>" <?= old('crop_id') == $crop['id'] ? 'selected' : '' ?>>
                                            <?= esc($crop['crop_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select a crop.</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Block Code -->
                            <div class="col-md-6 mb-3">
                                <label for="block_code" class="form-label">Block Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="block_code" name="block_code" 
                                       value="<?= old('block_code') ?>" required maxlength="50">
                                <div class="invalid-feedback">Please provide a block code.</div>
                            </div>

                            <!-- LLG Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                <select class="form-select" id="llg_id" name="llg_id" required>
                                    <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>>
                                            <?= esc($llg['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select an LLG.</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Ward Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="ward_id" class="form-label">Ward <span class="text-danger">*</span></label>
                                <select class="form-select" id="ward_id" name="ward_id" required>
                                    <option value="">Select Ward</option>
                                </select>
                                <div class="invalid-feedback">Please select a ward.</div>
                            </div>

                            <!-- Village -->
                            <div class="col-md-6 mb-3">
                                <label for="village" class="form-label">Village <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="village" name="village" 
                                       value="<?= old('village') ?>" required maxlength="100">
                                <div class="invalid-feedback">Please provide a village name.</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Block Site -->
                            <div class="col-md-12 mb-3">
                                <label for="block_site" class="form-label">Block Site <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="block_site" name="block_site" 
                                       value="<?= old('block_site') ?>" required maxlength="200"
                                       placeholder="Describe the specific location of the farm block">
                                <div class="invalid-feedback">Please provide a block site description.</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- GPS Coordinates -->
                            <div class="col-md-6 mb-3">
                                <label for="lon" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="lon" name="lon" 
                                       value="<?= old('lon') ?>" maxlength="50"
                                       placeholder="e.g., 143.95555">
                                <small class="form-text text-muted">Optional GPS longitude coordinate</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="lat" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="lat" name="lat" 
                                       value="<?= old('lat') ?>" maxlength="50"
                                       placeholder="e.g., -6.31444">
                                <small class="form-text text-muted">Optional GPS latitude coordinate</small>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Remarks -->
                            <div class="col-md-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                          placeholder="Additional notes or comments about this farm block"><?= old('remarks') ?></textarea>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="<?= base_url('staff/farms') ?>" class="btn btn-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Farm Block
                            </button>
                        </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Help Panel -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Help
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Creating a Farm Block</h6>
                    <ul class="small">
                        <li><strong>Farmer:</strong> Select the farmer who owns this block</li>
                        <li><strong>Crop:</strong> Choose the primary crop for this block</li>
                        <li><strong>Block Code:</strong> Unique identifier for this farm block</li>
                        <li><strong>Location:</strong> Select LLG, Ward, and specify village</li>
                        <li><strong>GPS:</strong> Optional coordinates for precise location</li>
                    </ul>
                    
                    <div class="alert alert-info">
                        <small><i class="fas fa-lightbulb"></i> 
                        <strong>Tip:</strong> Use descriptive block codes like "FB001" or "CORN-A1" for easy identification.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load wards when LLG is selected
document.getElementById('llg_id').addEventListener('change', function() {
    const llgId = this.value;
    const wardSelect = document.getElementById('ward_id');
    
    // Clear existing options
    wardSelect.innerHTML = '<option value="">Select Ward</option>';
    
    if (llgId) {
        fetch('<?= base_url('staff/farms/get-wards') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'llg_id=' + llgId
        })
        .then(response => response.json())
        .then(data => {
            data.forEach(ward => {
                const option = document.createElement('option');
                option.value = ward.id;
                option.textContent = ward.name;
                wardSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading wards:', error);
        });
    }
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
