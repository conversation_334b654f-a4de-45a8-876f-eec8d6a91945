<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/farm-blocks') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Farm Blocks
        </a>
        <div>
            <!-- Button trigger modal -->
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addBlockDataModal">
                <i class="fas fa-plus-circle"></i> Add Block Data
            </button>
            <!-- Modal -->
            <div class="modal fade" id="addBlockDataModal" tabindex="-1" aria-labelledby="addBlockDataModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title" id="addBlockDataModalLabel"><i class="fas fa-plus"></i> Add Block Data</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <?= form_open_multipart('staff/farms/add-block-data', ['id' => 'addBlockDataForm']) ?>
                        <div class="modal-body text-dark">
                            <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                            <input type="hidden" name="crop_id" value="<?= $crop['id'] ?>">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="action_type" class="form-label">Action Type *</label>
                                        <select class="form-select" name="action_type" required>
                                            <option value="add">Add</option>
                                            <option value="remove">Remove</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="action_reason" class="form-label">Action Reason *</label>
                                        <input type="text" class="form-control" id="action_reason" name="action_reason" required>
                                        <div class="form-text">eg. new planting, disease, disaster, etc.</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="action_date" class="form-label">Action Date *</label>
                                        <input type="date" class="form-control" id="action_date" name="action_date" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="number_of_plants" class="form-label">Number of Plants *</label>
                                        <input type="number" class="form-control" id="number_of_plants" name="number_of_plants" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="breed" class="form-label">Breed *</label>
                                        <input type="text" class="form-control" id="breed" name="breed" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="hectares" class="form-label">Hectares *</label>
                                        <input type="number" step="0.01" class="form-control" id="hectares" name="hectares" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                            </div>

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-success" id="btnAddBlockData">
                                <i class="fa fa-paper-plane"></i> Save Block Data
                            </button>
                        </div>
                        <?= form_close() ?>

                        <script>
                            $(document).ready(function() {
                                // Add keypress event listener to the form input fields
                                $('#addBlockDataForm input').keypress(function(e) {
                                    if (e.which == 13) {
                                        e.preventDefault(); // Prevent the default form submission
                                        $('#btnAddBlockData').click(); // Trigger the AJAX function
                                    }
                                });

                                $('#btnAddBlockData').on('click', function() {
                                    // Create FormData object to store form data
                                    var formData = new FormData($('#addBlockDataForm')[0]);

                                    // Send an AJAX request
                                    $.ajax({
                                        url: "<?= base_url('staff/farms/add-block-data'); ?>",
                                        type: 'POST',
                                        data: formData,
                                        contentType: false,
                                        processData: false,
                                        beforeSend: function() {
                                            // Display a loading indicator
                                            $('#btnAddBlockData').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');
                                        },
                                        success: function(response) {
                                            if (response.status === 'success') {
                                                toastr.success(response.message);
                                                setTimeout(function() {
                                                    location.reload();
                                                }, 1000);
                                            } else {
                                                toastr.error(response.message);
                                                setTimeout(function() {
                                                    location.reload();
                                                }, 2000);
                                            }
                                        },
                                        error: function(error) {
                                            console.log(error.responseText);
                                            toastr.error('An error occurred while saving data');
                                        }
                                    });
                                });
                            });
                        </script>

                    </div>
                </div>
            </div>



        </div>
    </div>
</div>

<div class="row">
    <!-- Farm Block Details Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Block Code</th>
                        <td><?= esc($block['block_code']) ?></td>
                    </tr>
                    <tr>
                        <th>Crop</th>
                        <td><?= esc($crop['item']) ?></td>
                    </tr>
                    <tr>
                        <th>Remarks</th>
                        <td><?= esc($block['remarks']) ?: 'No remarks' ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>


</div>




<?= $this->endSection() ?>