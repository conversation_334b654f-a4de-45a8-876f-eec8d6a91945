<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Back Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/farmers') ?>" class="text-success">Farmers</a></li>
            <li class="breadcrumb-item active" aria-current="page">View Farmer</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/farmers') ?>" class="btn btn-outline-success">
        <i class="fas fa-arrow-left"></i> Back to List
    </a>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Farmer Details</h5>
        <div>
            <a href="<?= base_url('staff/farmers/edit/' . $farmer['id']) ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Farmer Photo -->
            <div class="col-md-3 text-center mb-3">
                <?php if ($farmer['id_photo']): ?>
                    <img src="<?= imgcheck($farmer['id_photo']) ?>" 
                         class="img-thumbnail mb-2" style="max-width: 200px;" alt="Farmer Photo">
                <?php else: ?>
                    <img src="<?= base_url('public/assets/system_img/no-img.jpg') ?>" 
                         class="img-thumbnail mb-2" style="max-width: 200px;" alt="No Photo">
                <?php endif; ?>
                <div class="text-muted small">Farmer ID: <?= esc($farmer['farmer_code']) ?></div>
            </div>

            <!-- Personal Information -->
            <div class="col-md-4">
                <h6 class="border-bottom pb-2">Personal Information</h6>
                <table class="table table-borderless">
                    <tr>
                        <th width="35%">Full Name</th>
                        <td><?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></td>
                    </tr>
                    <tr>
                        <th>Date of Birth</th>
                        <td><?= date('d M Y', strtotime($farmer['date_of_birth'])) ?></td>
                    </tr>
                    <tr>
                        <th>Gender</th>
                        <td><?= esc($farmer['gender']) ?></td>
                    </tr>
                    <tr>
                        <th>Marital Status</th>
                        <td><?= esc($farmer['marital_status']) ?></td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <span class="badge bg-<?= $farmer['status'] === 'Active' ? 'success' : 'danger' ?>">
                                <?= esc($farmer['status']) ?>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Contact & Location -->
            <div class="col-md-5">
                <h6 class="border-bottom pb-2">Contact & Location</h6>
                <table class="table table-borderless">
                    <tr>
                        <th width="35%">Phone</th>
                        <td><?= esc($farmer['phone']) ?? 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td><?= esc($farmer['email']) ?? 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Address</th>
                        <td><?= esc($farmer['address']) ?? 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Village</th>
                        <td><?= esc($farmer['village']) ?? 'N/A' ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Education Information -->
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="border-bottom pb-2">Education Information</h6>
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="35%">Education Level</th>
                                <td>
                                    <?php 
                                    $education = model('EducationModel')
                                        ->where('id', $farmer['highest_education_id'])
                                        ->first();
                                    echo $education ? esc($education['name']) : 'N/A';
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Course Taken</th>
                                <td><?= esc($farmer['course_taken']) ?? 'N/A' ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Children Section -->
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="border-bottom pb-2">Children</h6>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Date of Birth</th>
                                <th>Age</th>
                                <th>Gender</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($children as $index => $child): ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td><?= esc($child['name']) ?></td>
                                <td><?= date('d M Y', strtotime($child['date_of_birth'])) ?></td>
                                <td><?= calculate_age($child['date_of_birth']) ?> years</td>
                                <td><?= esc($child['gender']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($children)): ?>
                            <tr>
                                <td colspan="5" class="text-center">No children recorded</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?> 