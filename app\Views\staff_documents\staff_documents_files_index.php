<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Action Buttons -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/office/documents') ?>" class="text-success">Document Folders</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= esc($folder['folder_name']) ?></li>
        </ol>
    </nav>
    <div>
        <a href="<?= base_url('staff/office/documents') ?>" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Folders
        </a>
        <a href="<?= base_url('staff/office/documents/files/create/' . $folder['id']) ?>" class="btn btn-success">
            <i class="fas fa-file-upload"></i> Upload File
        </a>
    </div>
</div>

<!-- Main Content -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-folder-open text-warning me-2"></i>
            Files in <?= esc($folder['folder_name']) ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')) : ?>
            <div class="alert alert-success">
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>
        
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($folder['description'])) : ?>
            <div class="alert alert-info">
                <strong>Folder Description:</strong> <?= esc($folder['description']) ?>
            </div>
        <?php endif; ?>

        <?php if (empty($files)) : ?>
            <div class="alert alert-warning">
                <i class="fas fa-info-circle me-2"></i> No files found in this folder. Upload a file to get started.
            </div>
        <?php else : ?>
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>File Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Description</th>
                            <th>Uploaded</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($files as $file) : ?>
                            <tr>
                                <td>
                                    <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="text-decoration-none">
                                        <?php
                                        $icon = 'fas fa-file';
                                        if (strpos($file['file_type'], 'image') !== false) {
                                            $icon = 'fas fa-file-image text-info';
                                        } elseif (strpos($file['file_type'], 'pdf') !== false) {
                                            $icon = 'fas fa-file-pdf text-danger';
                                        } elseif (strpos($file['file_type'], 'word') !== false || strpos($file['file_type'], 'document') !== false) {
                                            $icon = 'fas fa-file-word text-primary';
                                        } elseif (strpos($file['file_type'], 'excel') !== false || strpos($file['file_type'], 'spreadsheet') !== false) {
                                            $icon = 'fas fa-file-excel text-success';
                                        } elseif (strpos($file['file_type'], 'powerpoint') !== false || strpos($file['file_type'], 'presentation') !== false) {
                                            $icon = 'fas fa-file-powerpoint text-warning';
                                        } elseif (strpos($file['file_type'], 'zip') !== false || strpos($file['file_type'], 'archive') !== false) {
                                            $icon = 'fas fa-file-archive text-secondary';
                                        } elseif (strpos($file['file_type'], 'text') !== false) {
                                            $icon = 'fas fa-file-alt text-info';
                                        }
                                        ?>
                                        <i class="<?= $icon ?> me-2"></i>
                                        <?= esc($file['file_name']) ?>
                                    </a>
                                </td>
                                <td><?= esc($file['file_type']) ?></td>
                                <td><?= formatFileSize($file['file_size']) ?></td>
                                <td><?= esc($file['description']) ?></td>
                                <td><?= date('M d, Y', strtotime($file['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-primary" title="View File">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('staff/office/documents/files/edit/' . $file['id']) ?>" class="btn btn-sm btn-info" title="Edit File">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="confirmDelete('<?= base_url('staff/office/documents/files/delete/' . $file['id']) ?>')" 
                                                title="Delete File">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this file? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="deleteLink" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(deleteUrl) {
        document.getElementById('deleteLink').href = deleteUrl;
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }
</script>

<?php
// Helper function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<?= $this->endSection() ?>
