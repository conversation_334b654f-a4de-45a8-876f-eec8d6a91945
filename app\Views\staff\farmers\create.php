<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Display validation errors -->
<?php if (session()->getFlashdata('validation_errors')): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <h6><i class="fas fa-exclamation-triangle"></i> Validation Errors:</h6>
    <ul class="mb-0">
        <?php foreach (session()->getFlashdata('validation_errors') as $error): ?>
            <li><?= esc($error) ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Add New Farmer</h5>
    </div>
    <div class="card-body">
        <form action="<?= base_url('staff/farmers') ?>" method="post" enctype="multipart/form-data" id="farmerForm">
            <?= csrf_field() ?>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="given_name" class="form-label">Given Name *</label>
                    <input type="text" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['given_name']) ? 'is-invalid' : '' ?>"
                           id="given_name" name="given_name" value="<?= old('given_name') ?>" required>
                    <div class="invalid-feedback" id="given_name_error"></div>
                </div>
                <div class="col-md-6">
                    <label for="surname" class="form-label">Surname *</label>
                    <input type="text" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['surname']) ? 'is-invalid' : '' ?>"
                           id="surname" name="surname" value="<?= old('surname') ?>" required>
                    <div class="invalid-feedback" id="surname_error"></div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="date_of_birth" class="form-label">Date of Birth *</label>
                    <input type="date" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['date_of_birth']) ? 'is-invalid' : '' ?>"
                           id="date_of_birth" name="date_of_birth" value="<?= old('date_of_birth') ?>" required>
                    <div class="invalid-feedback" id="date_of_birth_error"></div>
                </div>
                <div class="col-md-4">
                    <label for="gender" class="form-label">Gender *</label>
                    <select class="form-select <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['gender']) ? 'is-invalid' : '' ?>"
                            id="gender" name="gender" required>
                        <option value="">Select Gender</option>
                        <option value="Male" <?= old('gender') == 'Male' ? 'selected' : '' ?>>Male</option>
                        <option value="Female" <?= old('gender') == 'Female' ? 'selected' : '' ?>>Female</option>
                    </select>
                    <div class="invalid-feedback" id="gender_error"></div>
                </div>
                <div class="col-md-4">
                    <label for="marital_status" class="form-label">Marital Status *</label>
                    <select class="form-select <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['marital_status']) ? 'is-invalid' : '' ?>"
                            id="marital_status" name="marital_status" required>
                        <option value="">Select Status</option>
                        <option value="Single" <?= old('marital_status') == 'Single' ? 'selected' : '' ?>>Single</option>
                        <option value="Married" <?= old('marital_status') == 'Married' ? 'selected' : '' ?>>Married</option>
                        <option value="Divorce" <?= old('marital_status') == 'Divorce' ? 'selected' : '' ?>>Divorce</option>
                        <option value="Widow" <?= old('marital_status') == 'Widow' ? 'selected' : '' ?>>Widow</option>
                        <option value="De-facto" <?= old('marital_status') == 'De-facto' ? 'selected' : '' ?>>De-facto</option>
                    </select>
                    <div class="invalid-feedback" id="marital_status_error"></div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="district_id" class="form-label">District</label>
                    <input type="text" class="form-control" value="<?= esc($district_name) ?>" readonly>
                    <input type="hidden" name="district_id" value="<?= session()->get('district_id') ?>">
                </div>
                <div class="col-md-4">
                    <label for="llg_id" class="form-label">LLG</label>
                    <select class="form-select <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['llg_id']) ? 'is-invalid' : '' ?>"
                            id="llg_id" name="llg_id">
                        <option value="">Select LLG</option>
                        <?php foreach ($llgs as $llg): ?>
                            <option value="<?= $llg['id'] ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>><?= esc($llg['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback" id="llg_id_error"></div>
                </div>
                <div class="col-md-4">
                    <label for="ward_id" class="form-label">Ward</label>
                    <select class="form-select <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['ward_id']) ? 'is-invalid' : '' ?>"
                            id="ward_id" name="ward_id" disabled>
                        <option value="">Select Ward</option>
                        <?php foreach ($wards as $ward): ?>
                            <option value="<?= $ward['id'] ?>" data-llg-id="<?= $ward['llg_id'] ?>" <?= old('ward_id') == $ward['id'] ? 'selected' : '' ?> style="display: none;">
                                <?= esc($ward['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback" id="ward_id_error"></div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="village" class="form-label">Village</label>
                    <input type="text" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['village']) ? 'is-invalid' : '' ?>"
                           id="village" name="village" value="<?= old('village') ?>" placeholder="Enter village name">
                    <div class="invalid-feedback" id="village_error"></div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="phone" class="form-label">Phone</label>
                    <input type="tel" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['phone']) ? 'is-invalid' : '' ?>"
                           id="phone" name="phone" value="<?= old('phone') ?>" placeholder="e.g., +************">
                    <div class="invalid-feedback" id="phone_error"></div>
                </div>
                <div class="col-md-4">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['email']) ? 'is-invalid' : '' ?>"
                           id="email" name="email" value="<?= old('email') ?>" placeholder="<EMAIL>">
                    <div class="invalid-feedback" id="email_error"></div>
                </div>
                <div class="col-md-4">
                    <label for="address" class="form-label">Address</label>
                    <input type="text" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['address']) ? 'is-invalid' : '' ?>"
                           id="address" name="address" value="<?= old('address') ?>" placeholder="Full address">
                    <div class="invalid-feedback" id="address_error"></div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="highest_education_id" class="form-label">Highest Education Level</label>
                    <select class="form-select <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['highest_education_id']) ? 'is-invalid' : '' ?>"
                            id="highest_education_id" name="highest_education_id">
                        <option value="">Select Education Level</option>
                        <?php if (!empty($education_levels)): ?>
                            <?php foreach ($education_levels as $edu): ?>
                                <option value="<?= $edu['id'] ?>" <?= old('highest_education_id') == $edu['id'] ? 'selected' : '' ?>><?= esc($edu['name']) ?></option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                    <div class="invalid-feedback" id="highest_education_id_error"></div>
                </div>
                <div class="col-md-4">
                    <label for="course_taken" class="form-label">Course Taken</label>
                    <input type="text" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['course_taken']) ? 'is-invalid' : '' ?>"
                           id="course_taken" name="course_taken" value="<?= old('course_taken') ?>" placeholder="e.g., Agriculture, Business">
                    <div class="invalid-feedback" id="course_taken_error"></div>
                </div>
                <div class="col-md-4">
                    <label for="id_photo" class="form-label">ID Photo</label>
                    <input type="file" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['id_photo']) ? 'is-invalid' : '' ?>"
                           id="id_photo" name="id_photo" accept="image/jpeg,image/jpg,image/png,image/gif">
                    <div class="invalid-feedback" id="id_photo_error"></div>
                    <small class="form-text text-muted">Max file size: 2MB. Allowed formats: JPEG, PNG, GIF</small>
                </div>
            </div>

            <div class="text-end">
                <a href="<?= base_url('staff/farmers') ?>" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                    Save Farmer
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form validation
    $('#farmerForm').on('submit', function(e) {
        let isValid = true;

        // Clear previous errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        // Validate required fields
        const requiredFields = ['given_name', 'surname', 'date_of_birth', 'gender', 'marital_status'];

        requiredFields.forEach(function(field) {
            const $field = $('#' + field);
            if (!$field.val().trim()) {
                $field.addClass('is-invalid');
                $('#' + field + '_error').text('This field is required');
                isValid = false;
            }
        });

        // Validate date of birth (must be in the past)
        const dob = new Date($('#date_of_birth').val());
        const today = new Date();
        if (dob >= today) {
            $('#date_of_birth').addClass('is-invalid');
            $('#date_of_birth_error').text('Date of birth must be before today');
            isValid = false;
        }

        // Validate email format if provided
        const email = $('#email').val();
        if (email && !isValidEmail(email)) {
            $('#email').addClass('is-invalid');
            $('#email_error').text('Please enter a valid email address');
            isValid = false;
        }

        // Validate phone format if provided
        const phone = $('#phone').val();
        if (phone && !isValidPhone(phone)) {
            $('#phone').addClass('is-invalid');
            $('#phone_error').text('Please enter a valid phone number');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $('.is-invalid').first().offset().top - 100
            }, 500);
        } else {
            // Show loading state
            const $submitBtn = $('#submitBtn');
            $submitBtn.prop('disabled', true);
            $submitBtn.find('.spinner-border').removeClass('d-none');
        }
    });

    // LLG change event - Filter wards based on selected LLG
    $('#llg_id').on('change', function() {
        const llgId = $(this).val();
        const wardSelect = $('#ward_id');

        if (llgId) {
            wardSelect.prop('disabled', false);

            // Hide all ward options first
            wardSelect.find('option').each(function() {
                if ($(this).val() === '') {
                    $(this).show(); // Keep the "Select Ward" option visible
                } else {
                    $(this).hide();
                }
            });

            // Show only wards that belong to the selected LLG
            wardSelect.find('option[data-llg-id="' + llgId + '"]').show();

            // Reset the selected value
            wardSelect.val('');
        } else {
            wardSelect.prop('disabled', true);
            wardSelect.val('');

            // Hide all ward options except the default
            wardSelect.find('option').each(function() {
                if ($(this).val() === '') {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    });

    // Real-time validation
    $('input, select').on('blur', function() {
        const $field = $(this);
        const fieldName = $field.attr('name');

        if ($field.hasClass('is-invalid')) {
            $field.removeClass('is-invalid');
            $('#' + fieldName + '_error').text('');
        }
    });

    // Helper functions
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]+$/;
        return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 7;
    }
});
</script>
<?= $this->endSection() ?> 