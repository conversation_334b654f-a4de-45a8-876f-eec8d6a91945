<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>


<div class="row">
    <!-- Farmer Edit Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 float-start">Edit Farmer</h5>
                
                <a href="<?= base_url('staff/farmers') ?>" class="btn btn-outline-success float-end">
        <i class="fas fa-arrow-left"></i> Back to List
    </a>
                
            </div>
            <div class="card-body">
                <form action="<?= base_url('staff/farmers/update/' . $farmer['id']) ?>" method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="given_name" class="form-label">Given Name *</label>
                            <input type="text" class="form-control" id="given_name" name="given_name"
                                value="<?= esc($farmer['given_name']) ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="surname" class="form-label">Surname *</label>
                            <input type="text" class="form-control" id="surname" name="surname"
                                value="<?= esc($farmer['surname']) ?>" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="date_of_birth" class="form-label">Date of Birth *</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth"
                                value="<?= esc($farmer['date_of_birth']) ?>" required>
                        </div>
                        <div class="col-md-4">
                            <label for="gender" class="form-label">Gender *</label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="Male" <?= $farmer['gender'] === 'Male' ? 'selected' : '' ?>>Male</option>
                                <option value="Female" <?= $farmer['gender'] === 'Female' ? 'selected' : '' ?>>Female</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="marital_status" class="form-label">Marital Status *</label>
                            <select class="form-select" id="marital_status" name="marital_status" required>
                                <option value="Single" <?= $farmer['marital_status'] === 'Single' ? 'selected' : '' ?>>Single</option>
                                <option value="Married" <?= $farmer['marital_status'] === 'Married' ? 'selected' : '' ?>>Married</option>
                                <option value="Divorce" <?= $farmer['marital_status'] === 'Divorce' ? 'selected' : '' ?>>Divorce</option>
                                <option value="Widow" <?= $farmer['marital_status'] === 'Widow' ? 'selected' : '' ?>>Widow</option>
                                <option value="De-facto" <?= $farmer['marital_status'] === 'De-facto' ? 'selected' : '' ?>>De-facto</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="district_id" class="form-label">District</label>
                            <input type="text" class="form-control" value="<?= esc($district_name) ?>" readonly>
                            <input type="hidden" name="district_id" value="<?= session()->get('district_id') ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="llg_id" class="form-label">LLG *</label>
                            <select class="form-select" id="llg_id" name="llg_id" required>
                                <option value="">Select LLG</option>
                                <?php foreach ($llgs as $llg): ?>
                                    <option value="<?= $llg['id'] ?>"
                                        <?= $farmer['llg_id'] == $llg['id'] ? 'selected' : '' ?>>
                                        <?= esc($llg['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="ward_id" class="form-label">Ward *</label>
                            <select class="form-select" id="ward_id" name="ward_id" required>
                                <option value="">Select Ward</option>
                                <?php foreach ($wards as $ward): ?>
                                    <option value="<?= $ward['id'] ?>" data-llg-id="<?= $ward['llg_id'] ?>"
                                        <?= $farmer['ward_id'] == $ward['id'] ? 'selected' : '' ?>
                                        <?= ($farmer['llg_id'] != $ward['llg_id']) ? 'style="display: none;"' : '' ?>>
                                        <?= esc($ward['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="village" class="form-label">Village</label>
                            <input type="text" class="form-control" id="village" name="village"
                                value="<?= esc($farmer['village']) ?>">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                value="<?= esc($farmer['phone']) ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email"
                                value="<?= esc($farmer['email']) ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?= $farmer['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= $farmer['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2"><?= esc($farmer['address']) ?></textarea>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="highest_education_id" class="form-label">Highest Education Level</label>
                            <select class="form-select" id="highest_education_id" name="highest_education_id">
                                <option value="">Select Education Level</option>
                                <?php foreach ($education_levels as $edu): ?>
                                    <option value="<?= $edu['id'] ?>" 
                                        <?= $farmer['highest_education_id'] == $edu['id'] ? 'selected' : '' ?>>
                                        <?= esc($edu['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="course_taken" class="form-label">Course Taken</label>
                            <input type="text" class="form-control" id="course_taken" name="course_taken" 
                                   value="<?= esc($farmer['course_taken']) ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="id_photo" class="form-label">ID Photo</label>
                            <?php if ($farmer['id_photo']): ?>
                                <div class="mb-2">
                                    <img src="<?= imgcheck($farmer['id_photo']) ?>" 
                                         class="img-thumbnail" style="height: 100px;">
                                </div>
                            <?php endif; ?>
                            <input type="file" class="form-control" id="id_photo" name="id_photo" accept="image/*">
                        </div>
                    </div>

                    <div class="text-end">
                        
                        <a href="<?= base_url('staff/farmers') ?>" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Farmer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Children Management -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Children</h5>
                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addChildModal">
                    <i class="fas fa-plus"></i> Add Child
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="childrenTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Gender</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($children as $child): ?>
                                <tr>
                                    <td><?= esc($child['name']) ?></td>
                                    <td><?= calculate_age($child['date_of_birth']) ?></td>
                                    <td><?= esc($child['gender']) ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-warning edit-child-btn" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editChildModal<?= $child['id'] ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <!-- <a href="<?= base_url('staff/farmers/delete-child/' . $child['id']) ?>"
                                            class="btn btn-sm btn-danger"
                                            onclick="return confirm('Are you sure you want to delete child: <?= esc($child['name']) ?>?')"
                                            title="Delete <?= esc($child['name']) ?>">
                                            <i class="fas fa-trash"></i>
                                        </a> -->
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Child Modal -->
<div class="modal fade" id="addChildModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Child</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('staff/farmers/add-child') ?>" method="post" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <input type="hidden" name="farmer_id" value="<?= $farmer['id'] ?>">
                <input type="hidden" name="org_id" value="<?= $farmer['org_id'] ?>">

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="child_name" class="form-label">Name *</label>
                        <input type="text" class="form-control" id="child_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="child_dob" class="form-label">Date of Birth *</label>
                        <input type="date" class="form-control" id="child_dob" name="date_of_birth" required>
                    </div>
                    <div class="mb-3">
                        <label for="child_gender" class="form-label">Gender *</label>
                        <select class="form-select" id="child_gender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Add Child</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Child Modals - Moved outside the table -->
<?php foreach ($children as $child): ?>
<div class="modal fade edit-child-modal" id="editChildModal<?= $child['id'] ?>" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Child: <?= esc($child['name']) ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('staff/farmers/update-child') ?>" method="post">
                <?= csrf_field() ?>
                <input type="hidden" name="id" value="<?= $child['id'] ?>">
                <input type="hidden" name="farmer_id" value="<?= $farmer['id'] ?>">
                <input type="hidden" name="org_id" value="<?= $farmer['org_id'] ?>">

                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Name *</label>
                        <input type="text" class="form-control" name="name" 
                               value="<?= esc($child['name']) ?>" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Date of Birth *</label>
                        <input type="date" class="form-control" name="date_of_birth" 
                               value="<?= esc($child['date_of_birth']) ?>" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Gender *</label>
                        <select class="form-select" name="gender" required>
                            <option value="Male" <?= $child['gender'] === 'Male' ? 'selected' : '' ?>>Male</option>
                            <option value="Female" <?= $child['gender'] === 'Female' ? 'selected' : '' ?>>Female</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update Child</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    console.log('Document ready');
    
    // Initialize DataTable with specific configuration
    const table = $('#childrenTable').DataTable({
        "responsive": true,
        "pageLength": 10,
        "drawCallback": function() {
            // Reinitialize modals after DataTable redraws
            initializeModals();
        }
    });

    function initializeModals() {
        // Proper modal initialization
        $('.edit-child-modal').each(function() {
            // Destroy existing modal instance if it exists
            if ($(this).data('bs.modal')) {
                $(this).data('bs.modal').dispose();
            }
            
            // Initialize new modal
            new bootstrap.Modal(this, {
                backdrop: 'static',
                keyboard: false
            });
        });
    }

    // Initial modal initialization
    initializeModals();

    // Handle modal events
    $('.edit-child-modal').on('shown.bs.modal', function() {
        $(this).find('input:first').focus();
    });

    $('.edit-child-modal').on('hidden.bs.modal', function() {
        // Reset validation states if any
        $(this).find('form').removeClass('was-validated');
    });

    // LLG change event - Filter wards based on selected LLG
    $('#llg_id').on('change', function() {
        const llgId = $(this).val();
        const wardSelect = $('#ward_id');

        if (llgId) {
            wardSelect.prop('disabled', false);

            // Hide all ward options first
            wardSelect.find('option').each(function() {
                if ($(this).val() === '') {
                    $(this).show(); // Keep the "Select Ward" option visible
                } else {
                    $(this).hide();
                }
            });

            // Show only wards that belong to the selected LLG
            wardSelect.find('option[data-llg-id="' + llgId + '"]').show();

            // Reset the selected value if current ward doesn't belong to selected LLG
            const currentWardLlgId = wardSelect.find('option:selected').attr('data-llg-id');
            if (currentWardLlgId && currentWardLlgId !== llgId) {
                wardSelect.val('');
            }
        } else {
            wardSelect.prop('disabled', true);
            wardSelect.val('');

            // Hide all ward options except the default
            wardSelect.find('option').each(function() {
                if ($(this).val() === '') {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    });
});
</script>
<?= $this->endSection() ?>