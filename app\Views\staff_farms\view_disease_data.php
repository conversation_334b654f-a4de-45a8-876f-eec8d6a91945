<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-12 mb-3">
        <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
            <a href="<?= base_url('staff/farms/diseases_data') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> <span class="d-none d-md-inline">Back to Diseases Data</span>
            </a>
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDiseaseDataModal">
                <i class="fas fa-plus-circle"></i> <span class="d-none d-md-inline">Add Disease Data</span>
            </button>
        </div>
    </div>
</div>

<div class="row g-3">
    <div class="col-12 col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-sm-6">
                        <p><strong>Block Code:</strong><br> <?= esc($block['block_code']) ?></p>
                    </div>
                    <div class="col-sm-6">
                        <p><strong>Crop:</strong><br> <?= esc($crop['crop_name']) ?></p>
                    </div>
                    <div class="col-12">
                        <p><strong>Farmer:</strong><br> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                    </div>
                    <div class="col-12">
                        <p><strong>Remarks:</strong><br> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Block Location</h5>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-sm-6">
                        <p><strong>Village:</strong><br> <?= esc($block['village']) ?></p>
                    </div>
                    <div class="col-sm-6">
                        <p><strong>Block Site:</strong><br> <?= esc($block['block_site']) ?></p>
                    </div>
                    <div class="col-12">
                        <p><strong>Province:</strong><br> 
                            <?= esc($province['name']) ?>, <?= esc($district['name']) ?>, 
                            <?= esc($llg['name']) ?>, <?= esc($ward['name']) ?>
                        </p>
                    </div>
                    <div class="col-12">
                        <p><strong>Coordinates:</strong><br> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Disease History Section -->
<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-history"></i> Disease History
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped text-nowrap" id="diseasesDataTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="text-nowrap">Date</th>
                        <th>Disease Type</th>
                        <th>Disease Name</th>
                        <th class="text-nowrap">Plants Affected</th>
                        <th>Breed</th>
                        <th class="text-nowrap">Hectares</th>
                        <th>Remarks</th>
                        <th>Created By</th>
                        <th class="text-nowrap">Created At</th>
                        <th>Updated By</th>
                        <th class="text-nowrap">Updated At</th>
                        <th class="text-nowrap">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($diseases_data as $data): ?>
                        <tr>
                            <td class="text-nowrap"><?= date('d M Y', strtotime($data['action_date'])) ?></td>
                            <td><?= esc($data['disease_type']) ?></td>
                            <td><?= esc($data['disease_name']) ?></td>
                            <td class="text-end text-nowrap"><?= number_format($data['number_of_plants']) ?></td>
                            <td><?= esc($data['breed']) ?></td>
                            <td class="text-end text-nowrap"><?= number_format($data['hectares'], 2) ?></td>
                            <td><?= esc($data['remarks']) ?: '-' ?></td>
                            <td><?php
                                foreach ($users as $user) {
                                    if ($user['id'] === $data['created_by']) {
                                        echo esc($user['name']);
                                        break;
                                    }
                                }
                                ?></td>
                            <td class="text-nowrap"><?= date('d M Y H:i', strtotime($data['created_at'])) ?></td>
                            <td><?php
                                foreach ($users as $user) {
                                    if ($user['id'] === $data['updated_by']) {
                                        echo esc($user['name']);
                                        break;
                                    }
                                }
                                ?></td>
                            <td class="text-nowrap"><?= date('d M Y H:i', strtotime($data['updated_at'])) ?></td>
                            <td class="text-nowrap">
                                <button type="button" class="btn btn-sm btn-primary edit-btn"
                                    data-id="<?= $data['id'] ?>"
                                    data-disease-type="<?= $data['disease_type'] ?>"
                                    data-disease-name="<?= $data['disease_name'] ?>"
                                    data-description="<?= $data['description'] ?>"
                                    data-number-of-plants="<?= $data['number_of_plants'] ?>"
                                    data-breed="<?= $data['breed'] ?>"
                                    data-action-date="<?= date('Y-m-d', strtotime($data['action_date'])) ?>"
                                    data-hectares="<?= $data['hectares'] ?>"
                                    data-remarks="<?= $data['remarks'] ?>">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Disease Data Modal -->
<div class="modal fade" id="addDiseaseDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add Disease Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addDiseaseDataForm" method="post">
            <div class="modal-body">
                <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                <input type="hidden" name="crop_id" value="<?= $block['crop_id'] ?>">

                <div class="row">

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Disease Type *</label>
                        <select name="disease_type" class="form-select" required>
                                <?php foreach ($infections as $infection): ?>
                                    <option value="<?= $infection['name'] ?>"><?= $infection['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Disease Name *</label>
                        <input type="text" name="disease_name" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-12">
                            <label class="form-label">Description</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>


                    <div class="mb-3 col-md-6">
                        <label class="form-label">Number of Plants *</label>
                        <input type="number" name="number_of_plants" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Breed *</label>
                        <input type="text" name="breed" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Hectares *</label>
                        <input type="number" step="0.01" name="hectares" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Action Date *</label>
                        <input type="date" name="action_date" class="form-control" required>
                    </div>



                    <div class="mb-3 col-md-12">
                        <label class="form-label">Remarks</label>
                        <textarea name="remarks" class="form-control" rows="3"></textarea>
                    </div>

                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success" id="btnAddDiseaseData">
                    <i class="fas fa-save"></i> Save Disease Data
                </button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Disease Data Modal -->
<div class="modal fade" id="editDiseaseDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Disease Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editDiseaseDataForm" method="post">
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">

                <div class="row">

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Disease Type *</label>
                        <select name="disease_type" id="edit_disease_type" class="form-select" required>
                            <?php foreach ($infections as $infection): ?>
                                <option value="<?= $infection['name'] ?>"><?= $infection['name'] ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Disease Name *</label>
                        <input type="text" name="disease_name" id="edit_disease_name" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-12">
                        <label class="form-label">Description</label>
                        <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                    </div>



                    <div class="mb-3 col-md-6">
                        <label class="form-label">Number of Plants *</label>
                        <input type="number" name="number_of_plants" id="edit_number_of_plants" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Breed *</label>
                        <input type="text" name="breed" id="edit_breed" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Hectares *</label>
                        <input type="number" step="0.01" name="hectares" id="edit_hectares" class="form-control" required>
                    </div>

                    <div class="mb-3 col-md-6">
                        <label class="form-label">Action Date *</label>
                        <input type="date" name="action_date" id="edit_action_date" class="form-control" required>
                    </div>
                    <div class="mb-3 col-md-12">
                        <label class="form-label">Remarks</label>
                        <textarea name="remarks" id="edit_remarks" class="form-control" rows="3"></textarea>
                    </div>

                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="btnUpdateDiseaseData">
                    <i class="fas fa-save"></i> Update Disease Data
                </button>
            </div>
            </form>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#diseasesDataTable').DataTable({
            responsive: false,
            processing: true,
            pageLength: 10,
            order: [[0, 'desc']],
            columnDefs: [
                { responsivePriority: 1, targets: [0, 1, 2] },
                { responsivePriority: 2, targets: [3, 5] },
                { responsivePriority: 3, targets: [-1] },
                { responsivePriority: 4, targets: '_all' }
            ],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        });

        // Make modals responsive
        $('.modal-dialog').addClass('modal-dialog-scrollable');
        
        // Ensure form fields are properly sized on mobile
        $('.form-control, .form-select').addClass('w-100');

        // Configure Toastr
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        // Add Disease Data Form Submit
        $('#addDiseaseDataForm').on('submit', function(e) {
            e.preventDefault();
            $.ajax({
                url: '<?= base_url('staff/farms/add-diseases-data') ?>',
                type: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    if (response.status === 'success') {
                        $('#addDiseaseDataModal').modal('hide');
                        toastr.success(response.message);
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('An error occurred while adding the disease data');
                }
            });
        });

        // Edit Button Click
        $('.edit-btn').on('click', function() {
            const data = $(this).data();

            $('#edit_id').val(data.id);
            $('#edit_disease_type').val(data.diseaseType);
            $('#edit_disease_name').val(data.diseaseName);
            $('#edit_description').val(data.description);
            $('#edit_number_of_plants').val(data.numberOfPlants);
            $('#edit_breed').val(data.breed);
            $('#edit_action_date').val(data.actionDate);
            $('#edit_hectares').val(data.hectares);
            $('#edit_remarks').val(data.remarks);

            $('#editDiseaseDataModal').modal('show');
        });

        // Edit Disease Data Form Submit
        $('#editDiseaseDataForm').on('submit', function(e) {
            e.preventDefault();
            $.ajax({
                url: '<?= base_url('staff/farms/update-diseases-data') ?>',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#editDiseaseDataModal').modal('hide');
                        toastr.success(response.message);
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error(xhr.responseText);
                    toastr.error('An error occurred while updating the disease data');
                }
            });
        });

        // Clear form when modal is closed
        $('#addDiseaseDataModal').on('hidden.bs.modal', function() {
            $('#addDiseaseDataForm')[0].reset();
        });

        $('#editDiseaseDataModal').on('hidden.bs.modal', function() {
            $('#editDiseaseDataForm')[0].reset();
        });
    });
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
