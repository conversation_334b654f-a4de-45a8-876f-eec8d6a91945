<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/fertilizer_data') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Fertilizer Data
        </a>
        <div>
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addFertilizerDataModal">
                <i class="fas fa-plus-circle"></i> Add Fertilizer Data
            </button>
        </div>
    </div>
</div>

<!-- Block Details Cards -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                
                <p><strong>Block Code:</strong> <?= esc($block['block_code']) ?></p>
                <p><strong>Crop:</strong> <?= esc($crop['item']) ?></p>
                <p><strong>Farmer:</strong> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                <p><strong>Remarks:</strong> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Location Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($block['village']) ?></p>
                <p><strong>Block Site:</strong> <?= esc($block['block_site']) ?></p>
                <p><strong>Province:</strong> <?= esc($province['name']) ?>, <?= esc($district['name']) ?>, <?= esc($llg['name']) ?>, <?= esc($ward['name']) ?></p>
                <p><strong>Coordinates:</strong> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Fertilizer Data Table -->
<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-history"></i> Fertilizer Application History</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="fertilizerDataTable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Fertilizer Type</th>
                        <th>Name</th>
                        <th>Brand</th>
                        <th>Quantity</th>
                        <th>Unit</th>
                        <th>Applied By</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fertilizer_data as $data): ?>
                        <tr>
                            <td><?= date('d M Y', strtotime($data['action_date'])) ?></td>
                            <td><?php
                                foreach ($fertilizers as $fertilizer) {
                                    if ($fertilizer['value'] === $data['fertilizer_id']) {
                                        echo esc($fertilizer['item']);
                                        break;
                                    }
                                }
                            ?></td>
                            <td><?= esc($data['name']) ?></td>
                            <td><?= esc($data['brand']) ?></td>
                            <td><?= number_format($data['quantity'], 2) ?></td>
                            <td><?= esc($data['unit_of_measure']) ?></td>
                            <td><?php
                                foreach ($users as $user) {
                                    if ($user['id'] === $data['created_by']) {
                                        echo esc($user['name']);
                                        break;
                                    }
                                }
                            ?></td>
                            <td><?= esc($data['remarks']) ?: '-' ?></td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary edit-btn" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editFertilizerDataModal"
                                        data-id="<?= $data['id'] ?>"
                                        data-name="<?= $data['name'] ?>"
                                        data-brand="<?= $data['brand'] ?>"
                                        data-quantity="<?= $data['quantity'] ?>"
                                        data-unit="<?= $data['unit_of_measure'] ?>"
                                        data-action_date="<?= $data['action_date'] ?>"
                                        data-remarks="<?= $data['remarks'] ?>">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Fertilizer Data Modal -->
<div class="modal fade" id="addFertilizerDataModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add Fertilizer Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/add-fertilizer-data', ['id' => 'addFertilizerForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                <input type="hidden" name="crop_id" value="<?= $block['crop_id'] ?>">

                <div class="mb-3">
                    <label for="fertilizer_type_id" class="form-label">Fertilizer Type *</label>
                    <select name="fertilizer_type_id" id="fertilizer_type_id" class="form-select" required>
                        <option value="">Select Fertilizer</option>
                        <?php foreach ($fertilizers as $fertilizer): ?>
                            <option value="<?= $fertilizer['value'] ?>" 
                                    data-hint="<?= esc($fertilizer['hints']) ?>"><?= esc($fertilizer['item']) ?></option>
                        <?php endforeach; ?>
                    </select>
                    <small class="text-success" id="fertilizer_type_id_help"></small>
                </div>
                <script>
                    document.getElementById('fertilizer_type_id').addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        const hint = selectedOption.dataset.hint || '';
                        document.getElementById('fertilizer_type_id_help').textContent = hint;
                    });
                </script>

                
                <div class="mb-3">
                    <label for="name" class="form-label">Fertilizer Name *</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>

                <div class="mb-3">
                    <label for="brand" class="form-label">Brand *</label>
                    <input type="text" class="form-control" id="brand" name="brand" required>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="quantity" name="quantity" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="unit_of_measure" class="form-label">Unit *</label>
                            <select class="form-select" id="unit_of_measure" name="unit_of_measure" required>
                                <option value="">Select Unit</option>
                                <option value="kg">Kilogram (kg)</option>
                                <option value="g">Gram (g)</option>
                                <option value="l">Liter (l)</option>
                                <option value="ml">Milliliter (ml)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="action_date" class="form-label">Application Date *</label>
                    <input type="date" class="form-control" id="action_date" name="action_date" required>
                </div>

                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Fertilizer Data Modal -->
<div class="modal fade" id="editFertilizerDataModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Fertilizer Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/update-fertilizer-data', ['id' => 'editFertilizerForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">
                <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                <input type="hidden" name="crop_id" value="<?= $block['crop_id'] ?>">

                <div class="mb-3">
                    <label for="edit_fertilizer_type_id" class="form-label">Fertilizer Type *</label>
                    <select name="fertilizer_type_id" id="edit_fertilizer_type_id" class="form-select" required>
                        <option value="">Select Fertilizer</option>
                        <?php foreach ($fertilizers as $fertilizer): ?>
                            <option value="<?= $fertilizer['value'] ?>" 
                                    data-hint="<?= esc($fertilizer['hints']) ?>"><?= esc($fertilizer['item']) ?></option>
                        <?php endforeach; ?>
                    </select>
                    <small class="text-success" id="edit_fertilizer_type_id_help"></small>
                </div>
                <script>
                    document.getElementById('edit_fertilizer_type_id').addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        const hint = selectedOption.dataset.hint || '';
                        document.getElementById('edit_fertilizer_type_id_help').textContent = hint;
                    });
                </script>

                <div class="mb-3">
                    <label for="edit_name" class="form-label">Fertilizer Name *</label>
                    <input type="text" class="form-control" id="edit_name" name="name" required>
                </div>

                <div class="mb-3">
                    <label for="edit_brand" class="form-label">Brand *</label>
                    <input type="text" class="form-control" id="edit_brand" name="brand" required>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_quantity" name="quantity" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_unit_of_measure" class="form-label">Unit *</label>
                            <select class="form-select" id="edit_unit_of_measure" name="unit_of_measure" required>
                                <option value="">Select Unit</option>
                                <option value="kg">Kilogram (kg)</option>
                                <option value="g">Gram (g)</option>
                                <option value="l">Liter (l)</option>
                                <option value="ml">Milliliter (ml)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="edit_action_date" class="form-label">Application Date *</label>
                    <input type="date" class="form-control" id="edit_action_date" name="action_date" required>
                </div>

                <div class="mb-3">
                    <label for="edit_remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#fertilizerDataTable').DataTable({
        responsive: true,
        order: [[0, 'desc']]
    });

    // Form submission handler
    $('#addFertilizerForm').submit(function(e) {
        e.preventDefault();
        
        // Validate form
        if (!validateForm($(this))) {
            return false;
        }
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            beforeSend: function() {
                // Disable submit button and show loading state
                $('#addFertilizerForm button[type="submit"]')
                    .prop('disabled', true)
                    .html('<i class="fas fa-spinner fa-spin"></i> Saving...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    toastr.success(response.message);
                    // Reset form and close modal
                    $('#addFertilizerForm')[0].reset();
                    $('#addFertilizerDataModal').modal('hide');
                    // Reload page after delay
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error(error);
                toastr.error('An error occurred while saving the data');
            },
            complete: function() {
                // Re-enable submit button
                $('#addFertilizerForm button[type="submit"]')
                    .prop('disabled', false)
                    .html('Save Data');
            }
        });
    });

    // Form validation function
    function validateForm($form) {
        let isValid = true;
        
        // Clear previous error states
        $form.find('.is-invalid').removeClass('is-invalid');
        
        // Required fields
        const requiredFields = [
            'fertilizer_type_id',
            'name',
            'brand',
            'quantity',
            'unit_of_measure',
            'action_date'
        ];
        
        requiredFields.forEach(field => {
            const $field = $form.find(`[name="${field}"]`);
            if (!$field.val()) {
                $field.addClass('is-invalid');
                isValid = false;
            }
        });
        
        // Validate quantity is positive
        const quantity = parseFloat($form.find('[name="quantity"]').val());
        if (quantity <= 0) {
            $form.find('[name="quantity"]').addClass('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            toastr.error('Please fill in all required fields correctly');
        }
        
        return isValid;
    }

    // Handle edit button click
    $('.edit-btn').click(function() {
        const data = $(this).data();
        $('#edit_id').val(data.id);
        $('#edit_name').val(data.name);
        $('#edit_brand').val(data.brand);
        $('#edit_quantity').val(data.quantity);
        $('#edit_unit_of_measure').val(data.unit);
        $('#edit_action_date').val(data.action_date);
        $('#edit_remarks').val(data.remarks);
    });
});
</script>
<?= $this->endSection() ?>