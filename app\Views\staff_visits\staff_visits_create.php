<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Back Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/extension/field-visits') ?>" class="text-success">Field Visits</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create Field Visit</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/extension/field-visits') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Field Visits
    </a>
</div>

<!-- Display Validation Errors -->
<?php if (session()->has('errors')): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach (session('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<!-- Create Field Visit Form -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">Create New Field Visit</h5>
    </div>
    <div class="card-body">
        <form action="<?= base_url('staff/extension/field-visits') ?>" method="post">
            <?= csrf_field() ?>

            <div class="row">
                <!-- Location Information -->
                <div class="col-md-6">
                    <h6 class="border-bottom pb-2 mb-3">Location Information</h6>

                    <div class="mb-3">
                        <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                        <select name="llg_id" id="llg_id" class="form-select" required>
                            <option value="">Select LLG</option>
                            <?php foreach ($llgs as $llg): ?>
                                <option value="<?= $llg['id'] ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>>
                                    <?= esc($llg['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="locations" class="form-label">Specific Locations</label>
                        <textarea name="locations" id="locations" class="form-control" rows="3"><?= old('locations') ?></textarea>
                        <small class="text-muted">Enter each location on a new line</small>
                    </div>

                    <div class="mb-3">
                        <label for="gps_coordinates" class="form-label">GPS Coordinates</label>
                        <textarea name="gps_coordinates" id="gps_coordinates" class="form-control" rows="3"><?= old('gps_coordinates') ?></textarea>
                        <small class="text-muted">Enter each GPS coordinate on a new line (e.g., -9.4438, 147.1803)</small>
                    </div>
                </div>

                <!-- Visit Details -->
                <div class="col-md-6">
                    <h6 class="border-bottom pb-2 mb-3">Visit Details</h6>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_start" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" name="date_start" id="date_start" class="form-control" value="<?= old('date_start') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_end" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" name="date_end" id="date_end" class="form-control" value="<?= old('date_end') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="purpose" class="form-label">Purpose <span class="text-danger">*</span></label>
                        <textarea name="purpose" id="purpose" class="form-control" rows="3" required><?= old('purpose') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="officers" class="form-label">Officers</label>
                        <select name="officers[]" id="officers" class="form-select" multiple>
                            <?php foreach ($officers as $officer): ?>
                                <option value="<?= $officer['id'] ?>">
                                    <?= esc($officer['name']) ?> (<?= esc($officer['position'] ?? 'Staff') ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-muted">Hold Ctrl/Cmd to select multiple officers</small>
                    </div>

                    <div class="mb-3">
                        <label for="achievements" class="form-label">Achievements</label>
                        <textarea name="achievements" id="achievements" class="form-control" rows="3"><?= old('achievements') ?></textarea>
                        <small class="text-muted">Enter each achievement on a new line</small>
                    </div>

                    <div class="mb-3">
                        <label for="beneficiaries" class="form-label">Beneficiaries</label>
                        <textarea name="beneficiaries" id="beneficiaries" class="form-control" rows="3"><?= old('beneficiaries') ?></textarea>
                        <small class="text-muted">Enter each beneficiary on a new line</small>
                    </div>
                </div>
            </div>

            <div class="mt-4 text-end">
                <button type="reset" class="btn btn-secondary me-2">Reset</button>
                <button type="submit" class="btn btn-success">Create Field Visit</button>
            </div>
        </form>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize select2 for multiple select
        $('#officers').select2({
            placeholder: 'Select officers',
            width: '100%'
        });

        // Date validation
        $('#date_end').change(function() {
            var startDate = new Date($('#date_start').val());
            var endDate = new Date($(this).val());

            if (endDate < startDate) {
                alert('End date cannot be earlier than start date');
                $(this).val('');
            }
        });

        $('#date_start').change(function() {
            var startDate = new Date($(this).val());
            var endDate = new Date($('#date_end').val());

            if ($('#date_end').val() && endDate < startDate) {
                alert('End date cannot be earlier than start date');
                $('#date_end').val('');
            }
        });
    });
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
