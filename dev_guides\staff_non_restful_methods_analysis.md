# Staff Routes and Controllers - Non-RESTful Methods Analysis

## Overview
This document analyzes all staff-related routes and controllers in the agristats application to identify methods that are not following RESTful principles, specifically methods that handle both GET and POST requests in the same method.

## Analysis Date
Generated on: 2025-08-05
**Updated on: 2025-08-05** - Non-RESTful method fixed, backward compatibility removed, FarmerInformationModel updated with soft delete

## Methodology
1. **Route Configuration Review**: Examined `app/Config/Routes.php` for staff route groups
2. **Controller Examination**: Analyzed all controllers in `app/Controllers/Staff/` directory
3. **RESTful Compliance Check**: Identified methods that:
   - Handle both GET and POST requests in the same method
   - Mix form display and form processing logic
   - Use request method validation (`getMethod()`) to branch logic

## Staff Route Groups Analyzed
- **Main Staff Routes**: `/staff` group with namespace `App\Controllers\Staff`
- **Farms Module**: `/staff/farms` routes
- **Livestock Module**: `/staff/livestock` routes
- **Reports Module**: `/staff/reports` routes
- **Workplan Module**: `/staff/workplan` routes

## Non-RESTful Methods Identified

### 1. Staff_Livestock.php ✅ **FIXED**

#### Method: `get_wards()` (Lines 116-153)
**Issue**: ~~Handles both AJAX and non-AJAX requests in the same method~~ **RESOLVED**

**Original Issue**: The method used conditional logic to handle both AJAX and non-AJAX requests in the same method.

**Fix Applied**: Refactored to only handle POST requests (backward compatibility removed):

```php
public function get_wards()
{
    $llg_id = $this->request->getPost('llg_id');

    if (!$llg_id) {
        return $this->response->setJSON([
            'success' => false,
            'message' => 'LLG ID is required',
            'wards' => []
        ]);
    }
    // ... rest of the processing logic
}
```

**Route**: `$routes->post('get-wards', 'Staff_Livestock::get_wards');`

**Status**: ✅ **RESOLVED** - Method now follows RESTful principles (backward compatibility removed)

### 2. Staff_Farms copy.php (Backup File)

#### Method: `get_wards()` (Lines 125-161)
**Issue**: Same as above - handles both AJAX and non-AJAX requests

**Note**: This appears to be a backup file and may not be actively used in the application.

### 3. Staff_Farms copy 2.php (Backup File)

#### Method: `get_wards()` (Lines 134-170)
**Issue**: Same as above - handles both AJAX and non-AJAX requests

**Note**: This appears to be a backup file and may not be actively used in the application.

## RESTful Methods (Correctly Implemented)

### Controllers Following RESTful Principles:
1. **Staff.php** - All methods are RESTful
2. **StaffController.php** - All methods are RESTful
3. **Staff_FarmsController.php** - All methods are RESTful
4. **FarmerController.php** - All methods are RESTful
5. **StaffCropsData.php** - All methods are RESTful
6. **StaffFertilizer.php** - All methods are RESTful
7. **StaffPesticides.php** - All methods are RESTful
8. **StaffDisease.php** - All methods are RESTful
9. **StaffHarvest.php** - All methods are RESTful
10. **StaffMarket.php** - All methods are RESTful
11. **StaffVisitsController.php** - All methods are RESTful
12. **StaffDocumentsController.php** - All methods are RESTful
13. **StaffClimateController.php** - All methods are RESTful

### Examples of Proper RESTful Implementation:

#### Form Display (GET) and Processing (POST) Separation:
```php
// GET method for displaying form
public function create()
{
    return view('staff/farmers/create', $data);
}

// POST method for processing form
public function store()
{
    $data = $this->request->getPost();
    // ... validation and processing
    return redirect()->to('staff/farmers');
}
```

#### AJAX Methods (POST only):
```php
public function add_farm_block()
{
    // Only handles POST requests
    $data = $this->request->getPost();
    // ... processing
    return $this->response->setJSON(['status' => 'success']);
}
```

## Route Configuration Analysis

### RESTful Route Patterns Found:
```php
// Proper RESTful routes
$routes->get('farmers', 'FarmerController::index');
$routes->get('farmers/create', 'FarmerController::create');
$routes->post('farmers', 'FarmerController::store');
$routes->get('farmers/edit/(:num)', 'FarmerController::edit/$1');
$routes->post('farmers/update/(:num)', 'FarmerController::update/$1');
$routes->get('farmers/delete/(:num)', 'FarmerController::delete/$1');
```

### AJAX Routes (Properly Separated):
```php
$routes->post('get_llgs', 'Staff_FarmsController::get_llgs');
$routes->post('get_wards', 'Staff_FarmsController::get_wards');
$routes->post('add_farm_block', 'Staff_FarmsController::add_farm_block');
```

## Summary

### Total Controllers Analyzed: 15+
### Non-RESTful Methods Found: ~~1 (active)~~ **0 (FIXED)** + 2 (backup files)
### RESTful Compliance Rate: **100%** (excluding backup files)

## Recommendations

### ✅ Completed Actions:

1. **✅ FIXED Staff_Livestock.php::get_wards() method**:
   - Removed conditional AJAX logic that violated RESTful principles
   - Method now properly handles only POST requests as intended
   - **Backward compatibility removed** for cleaner RESTful implementation

2. **✅ UPDATED FarmerInformationModel.php**:
   - Enabled soft delete functionality (`useSoftDeletes = true`)
   - Added `deleted_at` and `deleted_by` fields to allowedFields
   - Added `deletedField = 'deleted_at'` configuration
   - Added validation rules for `deleted_by` field
   - Added `setDeletedBy()` callback to automatically set deleted_by field
   - **Database structure**: Requires `deleted_at` and `deleted_by` columns in farmer_information table

### Database Migration Required:

**⚠️ IMPORTANT**: The following SQL must be executed to add soft delete columns to the farmer_information table:

```sql
ALTER TABLE farmer_information
ADD COLUMN deleted_at TIMESTAMP NULL DEFAULT NULL AFTER updated_by,
ADD COLUMN deleted_by INT(11) NULL DEFAULT NULL AFTER deleted_at;
```

### Remaining Actions:

1. **Execute database migration** (see above SQL)
2. **Clean up backup files**: Remove or archive the copy files:
   - `Staff_Farms copy.php`
   - `Staff_Farms copy 2.php`
   - `Staff_Farms copy 3.php`
   - `Staff_FarmsController copy.php`

### Best Practices to Maintain:

1. **Separate GET and POST logic** into different methods
2. **Use descriptive method names** that indicate their purpose
3. **Follow CodeIgniter 4 RESTful conventions**
4. **Validate request types** at the beginning of methods
5. **Use proper HTTP status codes** in responses

## Conclusion

The staff module now demonstrates **100% RESTful compliance** with all active methods following proper RESTful principles. The majority of controllers properly separate form display (GET) from form processing (POST) logic, and AJAX methods are correctly implemented to handle only their intended request types.

**✅ RESOLVED**: The identified issue in `Staff_Livestock.php::get_wards()` has been successfully fixed by removing the conditional AJAX check and ensuring the method only handles POST requests as intended. The method now follows RESTful principles with backward compatibility removed for cleaner implementation.

**✅ ENHANCED**: FarmerInformationModel.php has been updated with full soft delete functionality, including automatic tracking of who deleted records and when they were deleted.
