<?= $this->extend('layouts/staff_layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title"><?= esc($page_header) ?></h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('staff/farmers') ?>">Farmers</a></li>
                        <li class="breadcrumb-item active">Add New</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Add New Farmer</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('staff/farmers') ?>" method="post" enctype="multipart/form-data" id="farmerForm">
                        <?= csrf_field() ?>

                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Personal Information</h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="given_name" class="form-label">Given Name *</label>
                                <input type="text" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['given_name']) ? 'is-invalid' : '' ?>"
                                       id="given_name" name="given_name" value="<?= old('given_name') ?>" required>
                                <?php if (session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['given_name'])): ?>
                                    <div class="invalid-feedback"><?= session()->getFlashdata('validation_errors')['given_name'] ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <label for="surname" class="form-label">Surname *</label>
                                <input type="text" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['surname']) ? 'is-invalid' : '' ?>"
                                       id="surname" name="surname" value="<?= old('surname') ?>" required>
                                <?php if (session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['surname'])): ?>
                                    <div class="invalid-feedback"><?= session()->getFlashdata('validation_errors')['surname'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="date_of_birth" class="form-label">Date of Birth *</label>
                                <input type="date" class="form-control <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['date_of_birth']) ? 'is-invalid' : '' ?>"
                                       id="date_of_birth" name="date_of_birth" value="<?= old('date_of_birth') ?>" required>
                                <?php if (session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['date_of_birth'])): ?>
                                    <div class="invalid-feedback"><?= session()->getFlashdata('validation_errors')['date_of_birth'] ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4">
                                <label for="gender" class="form-label">Gender *</label>
                                <select class="form-select <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['gender']) ? 'is-invalid' : '' ?>"
                                        id="gender" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="Male" <?= old('gender') == 'Male' ? 'selected' : '' ?>>Male</option>
                                    <option value="Female" <?= old('gender') == 'Female' ? 'selected' : '' ?>>Female</option>
                                </select>
                                <?php if (session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['gender'])): ?>
                                    <div class="invalid-feedback"><?= session()->getFlashdata('validation_errors')['gender'] ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4">
                                <label for="marital_status" class="form-label">Marital Status *</label>
                                <select class="form-select <?= session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['marital_status']) ? 'is-invalid' : '' ?>"
                                        id="marital_status" name="marital_status" required>
                                    <option value="">Select Status</option>
                                    <option value="Single" <?= old('marital_status') == 'Single' ? 'selected' : '' ?>>Single</option>
                                    <option value="Married" <?= old('marital_status') == 'Married' ? 'selected' : '' ?>>Married</option>
                                    <option value="Divorce" <?= old('marital_status') == 'Divorce' ? 'selected' : '' ?>>Divorce</option>
                                    <option value="Widow" <?= old('marital_status') == 'Widow' ? 'selected' : '' ?>>Widow</option>
                                    <option value="De-facto" <?= old('marital_status') == 'De-facto' ? 'selected' : '' ?>>De-facto</option>
                                </select>
                                <?php if (session()->getFlashdata('validation_errors') && isset(session()->getFlashdata('validation_errors')['marital_status'])): ?>
                                    <div class="invalid-feedback"><?= session()->getFlashdata('validation_errors')['marital_status'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Location Information</h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="province_id" class="form-label">Province</label>
                                <select class="form-select" id="province_id" name="province_id">
                                    <option value="">Select Province</option>
                                    <?php foreach ($provinces as $province): ?>
                                        <option value="<?= $province['id'] ?>" <?= old('province_id', $selected_province_id) == $province['id'] ? 'selected' : '' ?>>
                                            <?= esc($province['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="district_id" class="form-label">District</label>
                                <select class="form-select" id="district_id" name="district_id">
                                    <option value="">Select District</option>
                                    <?php foreach ($districts as $district): ?>
                                        <option value="<?= $district['id'] ?>" <?= old('district_id', $selected_district_id) == $district['id'] ? 'selected' : '' ?>>
                                            <?= esc($district['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="llg_id" class="form-label">LLG</label>
                                <select class="form-select" id="llg_id" name="llg_id">
                                    <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>>
                                            <?= esc($llg['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="ward_id" class="form-label">Ward</label>
                                <select class="form-select" id="ward_id" name="ward_id" disabled>
                                    <option value="">Select Ward</option>
                                    <?php foreach ($wards as $ward): ?>
                                        <option value="<?= $ward['id'] ?>" data-llg-id="<?= $ward['llg_id'] ?>" <?= old('ward_id') == $ward['id'] ? 'selected' : '' ?> style="display: none;">
                                            <?= esc($ward['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="village" name="village" value="<?= old('village') ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="country_id" class="form-label">Country</label>
                                <select class="form-select" id="country_id" name="country_id">
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $country): ?>
                                        <option value="<?= $country['id'] ?>" <?= old('country_id', 1) == $country['id'] ? 'selected' : '' ?>>
                                            <?= esc($country['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Contact Information</h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= old('phone') ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= old('email') ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control" id="address" name="address" rows="3"><?= old('address') ?></textarea>
                            </div>
                        </div>

                        <!-- Education & Other Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Education & Other Information</h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="highest_education_id" class="form-label">Highest Education</label>
                                <select class="form-select" id="highest_education_id" name="highest_education_id">
                                    <option value="">Select Education Level</option>
                                    <?php foreach ($education_levels as $education): ?>
                                        <option value="<?= $education['id'] ?>" <?= old('highest_education_id') == $education['id'] ? 'selected' : '' ?>>
                                            <?= esc($education['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="course_taken" class="form-label">Course Taken</label>
                                <input type="text" class="form-control" id="course_taken" name="course_taken" value="<?= old('course_taken') ?>" placeholder="e.g., Agriculture, Business">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="id_photo" class="form-label">ID Photo</label>
                                <input type="file" class="form-control" id="id_photo" name="id_photo" accept="image/jpeg,image/jpg,image/png,image/gif">
                                <small class="form-text text-muted">Max file size: 2MB. Allowed formats: JPEG, PNG, GIF</small>
                            </div>
                        </div>

                        <div class="text-end">
                            <a href="<?= base_url('staff/farmers') ?>" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                Save Farmer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form validation
    $('#farmerForm').on('submit', function(e) {
        let isValid = true;

        // Clear previous errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        // Validate required fields
        const requiredFields = ['given_name', 'surname', 'date_of_birth', 'gender', 'marital_status'];

        requiredFields.forEach(function(field) {
            const $field = $('#' + field);
            if (!$field.val().trim()) {
                $field.addClass('is-invalid');
                $field.next('.invalid-feedback').text('This field is required');
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $('.is-invalid').first().offset().top - 100
            }, 500);
        } else {
            // Show loading state
            const $submitBtn = $('#submitBtn');
            $submitBtn.prop('disabled', true);
            $submitBtn.find('.spinner-border').removeClass('d-none');
        }
    });

    // LLG change event - Filter wards based on selected LLG
    $('#llg_id').on('change', function() {
        const llgId = $(this).val();
        const wardSelect = $('#ward_id');

        if (llgId) {
            wardSelect.prop('disabled', false);

            // Hide all ward options first
            wardSelect.find('option').each(function() {
                if ($(this).val() === '') {
                    $(this).show(); // Keep the "Select Ward" option visible
                } else {
                    $(this).hide();
                }
            });

            // Show only wards that belong to the selected LLG
            wardSelect.find('option[data-llg-id="' + llgId + '"]').show();

            // Reset the selected value
            wardSelect.val('');
        } else {
            wardSelect.prop('disabled', true);
            wardSelect.val('');

            // Hide all ward options except the default
            wardSelect.find('option').each(function() {
                if ($(this).val() === '') {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    });

    // Initialize ward dropdown based on current LLG selection
    if ($('#llg_id').val()) {
        $('#llg_id').trigger('change');
    }
});
</script>
<?= $this->endSection() ?>
