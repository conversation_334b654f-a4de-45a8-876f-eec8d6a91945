<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= $page_header ?? 'Climate Focus Management' ?></h5>
                <a href="<?= base_url('staff/tools/climate-data/new') ?>" class="btn btn-success">
                    <i class="fas fa-plus-circle me-1"></i> Add New Climate Focus
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="climateTable" class="table table-striped table-hover dt-responsive nowrap" style="width:100%">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Location</th>
                                <th>GPS Coordinates</th>
                                <th>District</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($climate_focus as $climate): ?>
                            <tr>
                                <td><?= $climate['id'] ?></td>
                                <td><?= esc($climate['location']) ?></td>
                                <td><?= esc($climate['gps']) ?></td>
                                <td><?= esc($climate['district_name']) ?></td>
                                <td>
                                    <?php if ($climate['status'] == 1): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('d M Y', strtotime($climate['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('staff/tools/climate-data/' . $climate['id']) ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('staff/tools/climate-data/' . $climate['id'] . '/edit') ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('staff/tools/climate-data/' . $climate['id'] . '/delete') ?>" class="btn btn-sm btn-danger delete-btn" data-id="<?= $climate['id'] ?>">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#climateTable').DataTable({
            responsive: true,
            order: [[0, 'desc']]
        });

        // Handle delete confirmation with SweetAlert
        $('.delete-btn').on('click', function(e) {
            e.preventDefault();
            const href = $(this).attr('href');
            
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = href;
                }
            });
        });
    });
</script>
<?= $this->endSection() ?>
