<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/farm-blocks') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Farm Blocks
        </a>
        <div>
            <!-- Button trigger modal -->
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addBlockDataModal">
                <i class="fas fa-plus-circle"></i> Add Block Data
            </button>
            <!-- Modal -->
            <div class="modal fade" id="addBlockDataModal" tabindex="-1" aria-labelledby="addBlockDataModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title" id="addBlockDataModalLabel"><i class="fas fa-plus"></i> Add Block Data</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <?= form_open_multipart('staff/farms/add-block-data', ['id' => 'addBlockDataForm']) ?>
                        <div class="modal-body text-dark">
                            <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                            <input type="hidden" name="crop_id" value="<?= $crop['id'] ?>">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="action_type" class="form-label">Action Type *</label>
                                        <select class="form-select" name="action_type" id="action_type" required>
                                            <option value="">Select action type</option>
                                            <option value="add">Add</option>
                                            <option value="remove">Remove</option>
                                        </select>
                                        <div class="invalid-feedback" id="action_type_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="action_reason" class="form-label">Action Reason *</label>
                                        <input type="text" class="form-control" id="action_reason" name="action_reason" required>
                                        <div class="form-text">eg. new planting, disease, disaster, etc.</div>
                                        <div class="invalid-feedback" id="action_reason_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="action_date" class="form-label">Action Date *</label>
                                        <input type="date" class="form-control" id="action_date" name="action_date" required>
                                        <div class="invalid-feedback" id="action_date_error"></div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="number_of_plants" class="form-label">Number of Plants *</label>
                                        <input type="number" class="form-control" id="number_of_plants" name="number_of_plants" required>
                                        <div class="invalid-feedback" id="number_of_plants_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="breed" class="form-label">Breed *</label>
                                        <input type="text" class="form-control" id="breed" name="breed" required>
                                        <div class="invalid-feedback" id="breed_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="hectares" class="form-label">Hectares *</label>
                                        <input type="number" step="0.01" class="form-control" id="hectares" name="hectares" required>
                                        <div class="invalid-feedback" id="hectares_error"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                            </div>

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-success" id="btnAddBlockData">
                                <i class="fa fa-paper-plane"></i> Save Block Data
                            </button>
                        </div>
                        <?= form_close() ?>

                        <script>
                            $(document).ready(function() {
                                // Add keypress event listener to the form input fields
                                $('#addBlockDataForm input').keypress(function(e) {
                                    if (e.which == 13) {
                                        e.preventDefault(); // Prevent the default form submission
                                        $('#btnAddBlockData').click(); // Trigger the AJAX function
                                    }
                                });

                                $('#btnAddBlockData').on('click', function() {
                                    // Reset previous error states
                                    $('.is-invalid').removeClass('is-invalid');
                                    $('.invalid-feedback').html('');

                                    // Validate required fields
                                    let isValid = true;
                                    const requiredFields = ['action_type', 'action_reason', 'action_date', 'number_of_plants', 'breed', 'hectares'];
                                    
                                    requiredFields.forEach(field => {
                                        const value = $(`#${field}`).val();
                                        if (!value || value.trim() === '') {
                                            $(`#${field}`).addClass('is-invalid');
                                            $(`#${field}_error`).html('This field is required');
                                            isValid = false;
                                        }
                                    });

                                    if (!isValid) {
                                        return;
                                    }

                                    // Create FormData object to store form data
                                    var formData = new FormData($('#addBlockDataForm')[0]);

                                    // Send an AJAX request
                                    $.ajax({
                                        url: "<?= base_url('staff/farms/add-block-data'); ?>",
                                        type: 'POST',
                                        data: formData,
                                        contentType: false,
                                        processData: false,
                                        beforeSend: function() {
                                            // Display a loading indicator
                                            $('#btnAddBlockData').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');
                                        },
                                        success: function(response) {
                                            if (response.status === 'success') {
                                                toastr.success(response.message);
                                                setTimeout(function() {
                                                    location.reload();
                                                }, 1000);
                                            } else {
                                                toastr.error(response.message);
                                                $('#btnAddBlockData').prop('disabled', false).html('<i class="fa fa-paper-plane"></i> Save Block Data');
                                            }
                                        },
                                        error: function(error) {
                                            console.log(error.responseText);
                                            toastr.error('An error occurred while saving data');
                                            $('#btnAddBlockData').prop('disabled', false).html('<i class="fa fa-paper-plane"></i> Save Block Data');
                                        }
                                    });
                                });
                            });
                        </script>

                    </div>
                </div>
            </div>



        </div>
    </div>
</div>

<div class="row">
    <!-- Farm Block Details Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Block Code:</strong> <?= esc($block['block_code']) ?></p>
                <p><strong>Crop:</strong> <?= esc($crop['item']) ?></p>
                <p><strong>Farmer:</strong> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                <p><strong>Remarks:</strong> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
            </div>
        </div>
    </div>
    
    <!-- Farm Block Location Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Location</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($block['village']) ?></p>
                <p><strong>Block Site:</strong> <?= esc($block['block_site']) ?></p>
                <p><strong>Province:</strong> <?= esc($province['name']) ?>, <?= esc($district['name']) ?>, <?= esc($llg['name']) ?>, <?= esc($ward['name']) ?></p>
                <p><strong>Coordinates:</strong> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
            </div>
        </div>
    </div>
    
</div>



<!-- After your existing cards, add this section -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Block History
                    <div class="float-end">
                        <span class="badge bg-warning">Total Plants: <?= $total_plants_added['total_plants_added'] - $total_plants_removed['total_plants_removed'] ?></span>
                        <span class="badge bg-success ms-2">Total Hectares: <?= $total_hectares_added['total_hectares_added'] - $total_hectares_removed['total_hectares_removed'] ?></span>
                    </div>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="blockDataTable">
                        <thead class="bg-light">
                            <tr>
                                <th>Date</th>
                                <th>Action</th>
                                <th>Reason</th>
                                <th>Plants</th>
                                <th>Breed</th>
                                <th>Hectares</th>
                                <th>Remarks</th>
                                <th>Created By</th>
                                <th>Created At</th>
                                <th>Updated By</th>
                                <th>Updated At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($block_data as $data): ?>
                                <tr>
                                    <td><?= date('d M Y', strtotime($data['action_date'])) ?></td>
                                    <td>
                                        <?php if ($data['action_type'] === 'add'): ?>
                                            <span class="badge bg-success">Add</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Remove</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= esc($data['action_reason']) ?></td>
                                    <td class="text-end">
                                        <?= number_format($data['number_of_plants']) ?>
                                    </td>
                                    <td><?= esc($data['breed']) ?></td>
                                    <td class="text-end">
                                        <?= number_format($data['hectares'], 2) ?>
                                    </td>
                                    <td><?= esc($data['remarks']) ?: '-' ?></td>
                                    <td><?php
                                    foreach ($users as $user) {
                                        if ($user['id'] === $data['created_by']) {
                                            echo $user['name'];
                                        }
                                    }
                                     ?></td>
                                    <td><?= date('d M Y H:i', strtotime($data['created_at'])) ?></td>
                                    <td><?php
                                    foreach ($users as $user) {
                                        if ($user['id'] === $data['updated_by']) {
                                            echo $user['name'];
                                        }
                                    }
                                    ?></td>
                                    <td><?= date('d M Y H:i', strtotime($data['updated_at'])) ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" 
                                                onclick="editBlockData(<?= $data['id'] ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            <?php if (empty($block_data)): ?>
                                <tr>
                                    <td colspan="9" class="text-center">No data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#blockDataTable').DataTable({
        order: [[0, 'desc']], // Sort by date descending
        pageLength: 10,
        responsive: true
    });
});

function editBlockData(id) {
    // Add your edit logic here
    alert('Edit functionality coming soon for ID: ' + id);
}


</script>




<?= $this->endSection() ?>