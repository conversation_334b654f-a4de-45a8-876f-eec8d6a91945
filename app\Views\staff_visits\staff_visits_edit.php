<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Back Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/extension/field-visits') ?>" class="text-success">Field Visits</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit Field Visit</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/extension/field-visits') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Field Visits
    </a>
</div>

<!-- Display Validation Errors -->
<?php if (session()->has('errors')): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach (session('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<!-- Edit Field Visit Form -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">Edit Field Visit</h5>
    </div>
    <div class="card-body">
        <form action="<?= base_url('staff/extension/field-visits/' . $visit['id']) ?>" method="post">
            <?= csrf_field() ?>

            <div class="row">
                <!-- Location Information -->
                <div class="col-md-6">
                    <h6 class="border-bottom pb-2 mb-3">Location Information</h6>

                    <!-- Country, Province, and District are retrieved from session -->

                    <div class="mb-3">
                        <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                        <select name="llg_id" id="llg_id" class="form-select" required>
                            <option value="">Select LLG</option>
                            <?php foreach ($llgs as $llg): ?>
                                <option value="<?= $llg['id'] ?>" <?= (old('llg_id', $visit['llg_id']) == $llg['id']) ? 'selected' : '' ?>>
                                    <?= esc($llg['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="locations" class="form-label">Specific Locations</label>
                        <?php
                            $locations = '';
                            if (!empty($visit['locations'])) {
                                $locationsArray = json_decode($visit['locations'], true);
                                if (is_array($locationsArray)) {
                                    $locations = implode("\n", $locationsArray);
                                }
                            }
                        ?>
                        <textarea name="locations" id="locations" class="form-control" rows="3"><?= old('locations', $locations) ?></textarea>
                        <small class="text-muted">Enter each location on a new line</small>
                    </div>

                    <div class="mb-3">
                        <label for="gps_coordinates" class="form-label">GPS Coordinates</label>
                        <?php
                            $gpsCoordinates = '';
                            if (!empty($visit['gps'])) {
                                $gpsArray = json_decode($visit['gps'], true);
                                if (is_array($gpsArray)) {
                                    $gpsCoordinates = implode("\n", $gpsArray);
                                }
                            }
                        ?>
                        <textarea name="gps_coordinates" id="gps_coordinates" class="form-control" rows="3"><?= old('gps_coordinates', $gpsCoordinates) ?></textarea>
                        <small class="text-muted">Enter each GPS coordinate on a new line (e.g., -9.4438, 147.1803)</small>
                    </div>
                </div>

                <!-- Visit Details -->
                <div class="col-md-6">
                    <h6 class="border-bottom pb-2 mb-3">Visit Details</h6>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_start" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" name="date_start" id="date_start" class="form-control" value="<?= old('date_start', date('Y-m-d', strtotime($visit['date_start']))) ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_end" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" name="date_end" id="date_end" class="form-control" value="<?= old('date_end', date('Y-m-d', strtotime($visit['date_end']))) ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="purpose" class="form-label">Purpose <span class="text-danger">*</span></label>
                        <textarea name="purpose" id="purpose" class="form-control" rows="3" required><?= old('purpose', $visit['purpose']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="officers" class="form-label">Officers</label>
                        <?php
                            $selectedOfficers = [];
                            if (!empty($visit['officers'])) {
                                $officersArray = json_decode($visit['officers'], true);
                                if (is_array($officersArray)) {
                                    foreach ($officersArray as $officer) {
                                        if (isset($officer['id'])) {
                                            $selectedOfficers[] = $officer['id'];
                                        }
                                    }
                                }
                            }
                        ?>
                        <select name="officers[]" id="officers" class="form-select" multiple>
                            <?php foreach ($officers as $officer): ?>
                                <option value="<?= $officer['id'] ?>" <?= in_array($officer['id'], $selectedOfficers) ? 'selected' : '' ?>>
                                    <?= esc($officer['name']) ?> (<?= esc($officer['position'] ?? 'Staff') ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-muted">Hold Ctrl/Cmd to select multiple officers</small>
                    </div>

                    <div class="mb-3">
                        <label for="achievements" class="form-label">Achievements</label>
                        <?php
                            $achievements = '';
                            if (!empty($visit['achievements'])) {
                                $achievementsArray = json_decode($visit['achievements'], true);
                                if (is_array($achievementsArray)) {
                                    $achievements = implode("\n", $achievementsArray);
                                }
                            }
                        ?>
                        <textarea name="achievements" id="achievements" class="form-control" rows="3"><?= old('achievements', $achievements) ?></textarea>
                        <small class="text-muted">Enter each achievement on a new line</small>
                    </div>

                    <div class="mb-3">
                        <label for="beneficiaries" class="form-label">Beneficiaries</label>
                        <?php
                            $beneficiaries = '';
                            if (!empty($visit['beneficiaries'])) {
                                $beneficiariesArray = json_decode($visit['beneficiaries'], true);
                                if (is_array($beneficiariesArray)) {
                                    $beneficiaries = implode("\n", $beneficiariesArray);
                                }
                            }
                        ?>
                        <textarea name="beneficiaries" id="beneficiaries" class="form-control" rows="3"><?= old('beneficiaries', $beneficiaries) ?></textarea>
                        <small class="text-muted">Enter each beneficiary on a new line</small>
                    </div>
                </div>
            </div>

            <div class="mt-4 text-end">
                <a href="<?= base_url('staff/extension/field-visits') ?>" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">Update Field Visit</button>
            </div>
        </form>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize select2 for multiple select
        $('#officers').select2({
            placeholder: 'Select officers',
            width: '100%'
        });

        // No need for province/district change events as they're retrieved from session

        // Date validation
        $('#date_end').change(function() {
            var startDate = new Date($('#date_start').val());
            var endDate = new Date($(this).val());

            if (endDate < startDate) {
                alert('End date cannot be earlier than start date');
                $(this).val('');
            }
        });

        $('#date_start').change(function() {
            var startDate = new Date($(this).val());
            var endDate = new Date($('#date_end').val());

            if ($('#date_end').val() && endDate < startDate) {
                alert('End date cannot be earlier than start date');
                $('#date_end').val('');
            }
        });
    });
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
