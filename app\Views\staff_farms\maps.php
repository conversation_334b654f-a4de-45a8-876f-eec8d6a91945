<?php
// Debug logging
log_message('debug', 'Farm blocks data: ' . json_encode($farm_blocks));
log_message('debug', 'Livestock blocks data: ' . json_encode($livestock_blocks));
?>

<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Add Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

<div class="row">
    <div class="col-md-12 mb-3">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-map-marked-alt"></i> Farm Blocks Map
                    <span class="float-end">
                        <small>Total Blocks: <?= count($farm_blocks) + count($livestock_blocks) ?></small>
                    </span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Filters</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Block Type</label>
                                    <select id="blockTypeFilter" class="form-select">
                                        <option value="all">All Blocks</option>
                                        <option value="crops">Crop Blocks</option>
                                        <option value="livestock">Livestock Blocks</option>
                                    </select>
                                </div>
                                <div id="cropFilterContainer" class="mb-3">
                                    <label class="form-label">Crop</label>
                                    <select id="cropFilter" class="form-select">
                                        <option value="">All Crops</option>
                                        <?php foreach ($crops as $crop): ?>
                                            <option value="<?= $crop['id'] ?>" 
                                                    data-color="<?= $crop['crop_color_code'] ?>">
                                                <?= $crop['crop_name'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Legend</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    <div style="width: 20px; height: 20px; background-color: #3388ff; border-radius: 50%; margin-right: 10px;"></div>
                                    <span>Crop Blocks</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div style="width: 20px; height: 20px; background-color: #ff4444; border-radius: 50%; margin-right: 10px;"></div>
                                    <span>Livestock Blocks</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-9">
                        <div id="map" style="height: 700px; border-radius: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<!-- Add Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
$(document).ready(function() {
    // Initialize the map centered on PNG
    var map = L.map('map').setView([-6.314993, 143.95555], 8);
    
    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Store markers in arrays by type
    var cropMarkers = [];
    var livestockMarkers = [];
    
    // Function to add markers
    function initializeMarkers() {
        const cropBlocks = <?= json_encode($farm_blocks) ?>;
        const livestockBlocks = <?= json_encode($livestock_blocks) ?>;
        
        console.log('Crop blocks data:', cropBlocks);
        console.log('Livestock blocks data:', livestockBlocks);

        // Add crop block markers
        cropBlocks.forEach(function(block) {
            try {
                if (!block.lat || !block.lon) {
                    console.warn(`Missing coordinates for crop block ${block.block_code}`);
                    return;
                }

                const lat = parseFloat(block.lat);
                const lon = parseFloat(block.lon);
                
                if (isNaN(lat) || isNaN(lon)) {
                    console.warn(`Invalid coordinates for crop block ${block.block_code}`);
                    return;
                }

                const marker = L.circleMarker([lat, lon], {
                    radius: 8,
                    fillColor: block.crop_color_code || '#3388ff',
                    color: "#000",
                    weight: 1,
                    opacity: 1,
                    fillOpacity: 0.8,
                    type: 'crop'
                });

                const content = `
                    <div class="popup-content">
                        <h6>${block.block_code}</h6>
                        <p><strong>Type:</strong> Crop Block</p>
                        <p><strong>Farmer:</strong> ${block.given_name} ${block.surname}</p>
                        <p><strong>Crop:</strong> ${block.crop_name}</p>
                        <p><strong>Location:</strong> ${block.village}, ${block.ward_name}, ${block.llg_name}, ${block.district_name}</p>
                        <p><strong>Coordinates:</strong> ${lat}, ${lon}</p>
                    </div>
                `;

                marker.bindPopup(content);
                
                // Add hover effect
                marker.on('mouseover', function(e) {
                    this.openPopup();
                    this.setStyle({
                        fillOpacity: 1,
                        radius: 10
                    });
                });
                
                marker.on('mouseout', function(e) {
                    this.setStyle({
                        fillOpacity: 0.8,
                        radius: 8
                    });
                });

                marker.addTo(map);
                cropMarkers.push(marker);
            } catch (error) {
                console.error(`Error adding crop marker for block ${block.block_code}:`, error);
            }
        });

        // Add livestock block markers
        livestockBlocks.forEach(function(block) {
            try {
                if (!block.lat || !block.lon) {
                    console.warn(`Missing coordinates for livestock block ${block.block_code}`);
                    return;
                }

                const lat = parseFloat(block.lat);
                const lon = parseFloat(block.lon);
                
                if (isNaN(lat) || isNaN(lon)) {
                    console.warn(`Invalid coordinates for livestock block ${block.block_code}`);
                    return;
                }

                const marker = L.circleMarker([lat, lon], {
                    radius: 8,
                    fillColor: '#ff4444',
                    color: "#000",
                    weight: 1,
                    opacity: 1,
                    fillOpacity: 0.8,
                    type: 'livestock'
                });

                const content = `
                    <div class="popup-content">
                        <h6>${block.block_code}</h6>
                        <p><strong>Type:</strong> Livestock Block</p>
                        <p><strong>Farmer:</strong> ${block.given_name} ${block.surname}</p>
                        <p><strong>Location:</strong> ${block.village}, ${block.ward_name}, ${block.llg_name}, ${block.district_name}</p>
                        <p><strong>Coordinates:</strong> ${lat}, ${lon}</p>
                    </div>
                `;

                marker.bindPopup(content);
                
                // Add hover effect
                marker.on('mouseover', function(e) {
                    this.openPopup();
                    this.setStyle({
                        fillOpacity: 1,
                        radius: 10
                    });
                });
                
                marker.on('mouseout', function(e) {
                    this.setStyle({
                        fillOpacity: 0.8,
                        radius: 8
                    });
                });

                marker.addTo(map);
                livestockMarkers.push(marker);
            } catch (error) {
                console.error(`Error adding livestock marker for block ${block.block_code}:`, error);
            }
        });

        // Fit map to all markers
        const allMarkers = [...cropMarkers, ...livestockMarkers];
        if (allMarkers.length > 0) {
            const group = L.featureGroup(allMarkers);
            map.fitBounds(group.getBounds().pad(0.1));
        }
    }

    // Initialize markers
    initializeMarkers();

    // Handle block type filter changes
    $('#blockTypeFilter').on('change', function() {
        const selectedType = $(this).val();
        
        // Show/hide crop filter based on selection
        if (selectedType === 'livestock') {
            $('#cropFilterContainer').hide();
        } else {
            $('#cropFilterContainer').show();
        }
        
        // Show/hide markers based on selection
        cropMarkers.forEach(marker => {
            if (selectedType === 'all' || selectedType === 'crops') {
                marker.addTo(map);
            } else {
                map.removeLayer(marker);
            }
        });
        
        livestockMarkers.forEach(marker => {
            if (selectedType === 'all' || selectedType === 'livestock') {
                marker.addTo(map);
            } else {
                map.removeLayer(marker);
            }
        });
    });

    // Handle crop filter changes
    $('#cropFilter').on('change', function() {
        const selectedCrop = $(this).val();
        const blocks = <?= json_encode($farm_blocks) ?>;
        
        // Clear existing crop markers
        cropMarkers.forEach(marker => map.removeLayer(marker));
        cropMarkers = [];

        // Filter and add new crop markers
        const filteredBlocks = selectedCrop ? 
            blocks.filter(block => block.crop_id === selectedCrop) : 
            blocks;

        filteredBlocks.forEach(block => {
            if (!block.lat || !block.lon) return;
            
            const lat = parseFloat(block.lat);
            const lon = parseFloat(block.lon);
            
            if (isNaN(lat) || isNaN(lon)) return;

            const marker = L.circleMarker([lat, lon], {
                radius: 8,
                fillColor: block.crop_color_code || '#3388ff',
                color: "#000",
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8,
                type: 'crop'
            });

            const content = `
                <div class="popup-content">
                    <h6>${block.block_code}</h6>
                    <p><strong>Type:</strong> Crop Block</p>
                    <p><strong>Farmer:</strong> ${block.given_name} ${block.surname}</p>
                    <p><strong>Crop:</strong> ${block.crop_name}</p>
                    <p><strong>Location:</strong> ${block.village}, ${block.ward_name}, ${block.llg_name}, ${block.district_name}</p>
                    <p><strong>Coordinates:</strong> ${lat}, ${lon}</p>
                </div>
            `;

            marker.bindPopup(content);
            marker.addTo(map);
            cropMarkers.push(marker);
        });

        // Update map bounds
        const allMarkers = [...cropMarkers, ...livestockMarkers];
        if (allMarkers.length > 0) {
            const group = L.featureGroup(allMarkers);
            map.fitBounds(group.getBounds().pad(0.1));
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 