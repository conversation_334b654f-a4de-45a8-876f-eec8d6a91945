<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title mb-0">Training Dashboard</h4>
                    </div>
                    <div class="col-auto">
                        <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-primary">
                            <i class="fas fa-list"></i> View All Trainings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row align-items-center no-gutters">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Trainings</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalTrainings ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row align-items-center no-gutters">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Participants</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalParticipants ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row align-items-center no-gutters">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Average Participants</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $averageParticipants ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row align-items-center no-gutters">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Completed Trainings</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['1'] ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gender Breakdown -->
<div class="row mb-4">
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Gender Distribution</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-pie mb-4">
                            <canvas id="genderPieChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Gender</th>
                                        <th>Count</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><i class="fas fa-mars text-primary"></i> Male</td>
                                        <td><?= $maleParticipants ?></td>
                                        <td><?= $malePercentage ?>%</td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-venus text-danger"></i> Female</td>
                                        <td><?= $femaleParticipants ?></td>
                                        <td><?= $femalePercentage ?>%</td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-genderless text-secondary"></i> Other</td>
                                        <td><?= $otherParticipants ?></td>
                                        <td><?= $otherPercentage ?>%</td>
                                    </tr>
                                    <tr class="table-active">
                                        <td><strong>Total</strong></td>
                                        <td><strong><?= $totalParticipants ?></strong></td>
                                        <td><strong>100%</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Training Status</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie">
                    <canvas id="statusPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Monthly Trainings Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Monthly Trainings (<?= date('Y') ?>)</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="monthlyTrainingsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Breakdown -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Training Status</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie">
                    <canvas id="statusPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Topics and Locations Charts -->
<div class="row mb-4">
    <!-- Training Topics Chart -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Top Training Topics</h6>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="topicBarChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Locations Chart -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Top Training Locations</h6>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="locationBarChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Trainings -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Trainings</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Topic</th>
                                <th>Location</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Participants</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentTrainings as $training): ?>
                                <?php
                                    $locations = json_decode($training['locations'], true);
                                    $venue = $locations['venue'] ?? 'N/A';
                                    $location = $locations['location'] ?? 'N/A';

                                    $statusLabels = [
                                        '1' => '<span class="badge bg-success">Completed</span>',
                                        '2' => '<span class="badge bg-warning">Ongoing</span>',
                                        '3' => '<span class="badge bg-primary">Scheduled</span>',
                                        '4' => '<span class="badge bg-danger">Cancelled</span>'
                                    ];

                                    $statusLabel = $statusLabels[$training['status']] ?? '<span class="badge bg-secondary">Unknown</span>';

                                    // Count participants
                                    $attendees = json_decode($training['attendees'], true);
                                    $participantCount = is_array($attendees) ? count($attendees) : 0;
                                ?>
                                <tr>
                                    <td><?= esc($training['topic']) ?></td>
                                    <td><?= esc($venue) ?>, <?= esc($location) ?></td>
                                    <td><?= date('d-M-Y', strtotime($training['date_start'])) ?> to <?= date('d-M-Y', strtotime($training['date_end'])) ?></td>
                                    <td><?= $statusLabel ?></td>
                                    <td class="text-center"><?= $participantCount ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?= base_url('staff/extension/trainings/view/' . $training['id']) ?>"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('staff/extension/trainings/participants/' . $training['id']) ?>"
                                               class="btn btn-sm btn-primary" title="Manage Participants">
                                                <i class="fas fa-users"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>

                            <?php if (empty($recentTrainings)): ?>
                                <tr>
                                    <td colspan="6" class="text-center">No trainings found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>

<script>
    // Parse data from PHP
    const monthlyData = <?= json_encode(array_values($monthlyTrainings)) ?>;
    const statusData = [
        <?= $statusCounts['1'] ?>, // Completed
        <?= $statusCounts['2'] ?>, // Ongoing
        <?= $statusCounts['3'] ?>, // Scheduled
        <?= $statusCounts['4'] ?>  // Cancelled
    ];
    const genderData = [
        <?= $maleParticipants ?>, // Male
        <?= $femaleParticipants ?>, // Female
        <?= $otherParticipants ?>  // Other
    ];
    const topicLabels = <?= json_encode(array_keys($topicCounts)) ?>;
    const topicData = <?= json_encode(array_values($topicCounts)) ?>;
    const locationLabels = <?= json_encode(array_keys($locationTrainings)) ?>;
    const locationData = <?= json_encode(array_values($locationTrainings)) ?>;

    // Helper for background colors
    function generateColors(count, alpha = 0.7) {
        const colors = [
            `rgba(78, 115, 223, ${alpha})`,
            `rgba(28, 200, 138, ${alpha})`,
            `rgba(54, 185, 204, ${alpha})`,
            `rgba(246, 194, 62, ${alpha})`,
            `rgba(231, 74, 59, ${alpha})`,
            `rgba(133, 135, 150, ${alpha})`,
            `rgba(105, 57, 219, ${alpha})`,
            `rgba(0, 138, 92, ${alpha})`,
            `rgba(0, 158, 176, ${alpha})`,
            `rgba(224, 168, 15, ${alpha})`,
        ];

        return colors.slice(0, count);
    }

    // Create month labels
    const monthLabels = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];

    // Monthly Trainings Line Chart
    const monthlyTrainingsChart = new Chart(
        document.getElementById('monthlyTrainingsChart').getContext('2d'),
        {
            type: 'line',
            data: {
                labels: monthLabels,
                datasets: [{
                    label: 'Number of Trainings',
                    data: monthlyData,
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0 // Only show integers
                        }
                    }
                }
            }
        }
    );

    // Gender Pie Chart
    const genderPieChart = new Chart(
        document.getElementById('genderPieChart').getContext('2d'),
        {
            type: 'doughnut',
            data: {
                labels: ['Male', 'Female', 'Other'],
                datasets: [{
                    data: genderData,
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.8)', // Blue for Male
                        'rgba(255, 99, 132, 0.8)',  // Pink for Female
                        'rgba(201, 203, 207, 0.8)'  // Grey for Other
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(201, 203, 207, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        }
    );

    // Status Pie Chart
    const statusPieChart = new Chart(
        document.getElementById('statusPieChart').getContext('2d'),
        {
            type: 'doughnut',
            data: {
                labels: ['Completed', 'Ongoing', 'Scheduled', 'Cancelled'],
                datasets: [{
                    data: statusData,
                    backgroundColor: [
                        'rgba(28, 200, 138, 0.8)',
                        'rgba(246, 194, 62, 0.8)',
                        'rgba(54, 185, 204, 0.8)',
                        'rgba(231, 74, 59, 0.8)'
                    ],
                    borderColor: [
                        'rgba(28, 200, 138, 1)',
                        'rgba(246, 194, 62, 1)',
                        'rgba(54, 185, 204, 1)',
                        'rgba(231, 74, 59, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        }
    );

    // Topic Bar Chart
    const topicBarChart = new Chart(
        document.getElementById('topicBarChart').getContext('2d'),
        {
            type: 'bar',
            data: {
                labels: topicLabels,
                datasets: [{
                    label: 'Number of Trainings',
                    data: topicData,
                    backgroundColor: generateColors(topicLabels.length),
                    borderColor: generateColors(topicLabels.length, 1),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        }
    );

    // Location Bar Chart
    const locationBarChart = new Chart(
        document.getElementById('locationBarChart').getContext('2d'),
        {
            type: 'bar',
            data: {
                labels: locationLabels,
                datasets: [{
                    label: 'Number of Trainings',
                    data: locationData,
                    backgroundColor: generateColors(locationLabels.length),
                    borderColor: generateColors(locationLabels.length, 1),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        }
    );
</script>
<?= $this->endSection() ?>