<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3">
        <a href="<?= base_url('staff/farms/view-market-data/' . $farmer['id']) ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Marketing Data
        </a>
    </div>
</div>

<!-- Farmer Details Card -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-user"></i> Farmer Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Farmer Code:</strong> <?= esc($farmer['farmer_code']) ?></p>
                <p><strong>Name:</strong> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                <p><strong>Gender:</strong> <?= esc($farmer['gender']) ?></p>
                <p><strong>Contact:</strong> <?= esc($farmer['phone']) ?: 'N/A' ?></p>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Location Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($farmer['village']) ?></p>
                <p><strong>Address:</strong> <?= esc($farmer['address']) ?: 'N/A' ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Edit Marketing Data Form -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-edit"></i> Edit Marketing Data</h5>
    </div>
    <div class="card-body">
        <?php
        // Debug line - remove after testing
     //   echo '<pre>Debug: '; print_r($marketing_data); echo '</pre>';
        ?>
        <?= form_open('staff/farms/update-market-data', ['id' => 'editMarketForm']) ?>
            <input type="hidden" name="id" value="<?= esc($marketing_data['id']) ?>">
            <input type="hidden" name="farmer_id" value="<?= esc($farmer['id']) ?>">

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="crop_id" class="form-label">Crop *</label>
                        <select name="crop_id" id="crop_id" class="form-select select2bs4" required>
                            <option value="">Select Crop</option>
                            <?php foreach ($crops as $crop): ?>
                                <option value="<?= $crop['id'] ?>" <?= ($crop['id'] == $marketing_data['crop_id']) ? 'selected' : '' ?>>
                                    <?= esc($crop['crop_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="block_id" class="form-label">Farm Block</label>
                        <select name="block_id" id="block_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Farm Block">
                            <option value=""></option>
                            <?php foreach ($farm_blocks as $block): ?>
                                <option value="<?= $block['id'] ?>" <?= ($block['id'] == $marketing_data['block_id']) ? 'selected' : '' ?>>
                                    <?= esc($block['block_code']) ?> - <?= esc($block['block_site']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Optional. Select the farm block where this product came from.</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="product" class="form-label">Product Name *</label>
                        <input type="text" class="form-control" id="product" name="product" value="<?= esc($marketing_data['product']) ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="market_stage" class="form-label">Market Stage *</label>
                        <select class="form-select" id="market_stage" name="market_stage" required>
                            <option value="">Select Stage</option>
                            <option value="harvest" <?= ($marketing_data['market_stage'] == 'harvest') ? 'selected' : '' ?>>Harvest</option>
                            <option value="primary" <?= ($marketing_data['market_stage'] == 'primary') ? 'selected' : '' ?>>Primary</option>
                            <option value="secondary" <?= ($marketing_data['market_stage'] == 'secondary') ? 'selected' : '' ?>>Secondary</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="product_type" class="form-label">Product Type *</label>
                        <input type="text" class="form-control" id="product_type" name="product_type" value="<?= esc($marketing_data['product_type']) ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"><?= esc($marketing_data['description']) ?></textarea>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="unit" class="form-label">Unit *</label>
                        <input type="number" step="0.01" class="form-control" id="unit" name="unit" value="<?= esc($marketing_data['unit']) ?>" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="unit_of_measure" class="form-label">Unit of Measure *</label>
                        <select name="unit_of_measure" id="unit_of_measure" class="form-select" required>
                            <option value="">Select Unit of Measure</option>
                            <option value="kg" <?= ($marketing_data['unit_of_measure'] == 'kg') ? 'selected' : '' ?>>Kilograms</option>
                            <option value="g" <?= ($marketing_data['unit_of_measure'] == 'g') ? 'selected' : '' ?>>Grams</option>
                            <option value="l" <?= ($marketing_data['unit_of_measure'] == 'l') ? 'selected' : '' ?>>Liters</option>
                            <option value="ml" <?= ($marketing_data['unit_of_measure'] == 'ml') ? 'selected' : '' ?>>Milliliters</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity *</label>
                        <input type="number" step="0.01" class="form-control" id="quantity" name="quantity" value="<?= esc($marketing_data['quantity']) ?>" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="market_price_per_unit" class="form-label">Price per Unit *</label>
                        <input type="number" step="0.01" class="form-control" id="market_price_per_unit" name="market_price_per_unit" value="<?= esc($marketing_data['market_price_per_unit']) ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="market_date" class="form-label">Market Date *</label>
                        <input type="date" class="form-control" id="market_date" name="market_date" value="<?= esc($marketing_data['market_date']) ?>" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="total_freight_cost" class="form-label">Total Freight Cost</label>
                        <div class="input-group">
                            <span class="input-group-text">K</span>
                            <input type="number" step="0.01" min="0" class="form-control" id="total_freight_cost" name="total_freight_cost" value="<?= esc($marketing_data['total_freight_cost']) ?>" placeholder="Enter freight cost">
                        </div>
                        <div class="form-text">Optional. Enter the total cost of transportation.</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="selling_location" class="form-label">Selling Location</label>
                        <input type="text" class="form-control" id="selling_location" name="selling_location" value="<?= esc($marketing_data['selling_location']) ?>" placeholder="Enter specific selling location">
                        <div class="form-text">Optional. Enter the specific location where the produce was sold.</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="province_id" class="form-label">Province *</label>
                        <select name="province_id" id="province_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Province" required>
                            <option value=""></option>
                            <?php foreach ($provinces as $province): ?>
                                <option value="<?= $province['id'] ?>" <?= ($province['id'] == $marketing_data['province_id']) ? 'selected' : '' ?>>
                                    <?= esc($province['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="district_id" class="form-label">District *</label>
                        <select name="district_id" id="district_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select District" required>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="llg_id" class="form-label">LLG *</label>
                        <select name="llg_id" id="llg_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select LLG" required>
                            <option value=""></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="buyer_id" class="form-label">Buyer *</label>
                <select name="buyer_id" id="buyer_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Buyer" required>
                    <option value=""></option>
                    <?php foreach ($buyers as $buyer): ?>
                        <option value="<?= $buyer['id'] ?>" <?= ($buyer['id'] == $marketing_data['buyer_id']) ? 'selected' : '' ?>>
                            <?= esc($buyer['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="mb-3">
                <label for="remarks" class="form-label">Remarks</label>
                <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Enter any additional remarks"><?= esc($marketing_data['remarks']) ?></textarea>
            </div>

            <div class="text-end">
                <button type="button" class="btn btn-secondary" onclick="history.back()">Cancel</button>
                <button type="submit" class="btn btn-primary">Update Data</button>
            </div>
        <?= form_close() ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2bs4').select2({
        theme: 'bootstrap-5'
    });

    // Handle province change
    $('#province_id').on('change', function() {
        const provinceId = $(this).val();
        if (provinceId) {
            $.post(`<?= base_url('api/get_districts') ?>`, { province_id: provinceId }, function(response) {
                let options = '<option value=""></option>';
                response.forEach(district => {
                    const selected = district.id == '<?= $marketing_data['district_id'] ?>' ? 'selected' : '';
                    options += `<option value="${district.id}" ${selected}>${district.name}</option>`;
                });
                $('#district_id').html(options).trigger('change');
            });
        } else {
            $('#district_id').html('<option value=""></option>').trigger('change');
            $('#llg_id').html('<option value=""></option>').trigger('change');
        }
    });

    // Handle district change
    $('#district_id').on('change', function() {
        const districtId = $(this).val();
        if (districtId) {
            $.post(`<?= base_url('api/get_llgs') ?>`, { district_id: districtId }, function(response) {
                let options = '<option value=""></option>';
                response.forEach(llg => {
                    const selected = llg.id == '<?= $marketing_data['llg_id'] ?>' ? 'selected' : '';
                    options += `<option value="${llg.id}" ${selected}>${llg.name}</option>`;
                });
                $('#llg_id').html(options).trigger('change');
            });
        } else {
            $('#llg_id').html('<option value=""></option>').trigger('change');
        }
    });

    // Trigger initial province change to load districts
    if ($('#province_id').val()) {
        $('#province_id').trigger('change');
    }

    // Form submission handler
    $('#editMarketForm').submit(function(e) {
        e.preventDefault();
        
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            beforeSend: function() {
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    toastr.success(response.message);
                    setTimeout(() => window.location.href = '<?= base_url('staff/farms/view-market-data/' . $farmer['id']) ?>', 1500);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while processing your request');
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
<?= $this->endSection() ?> 