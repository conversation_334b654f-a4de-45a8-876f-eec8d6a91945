<?= $this->extend('templates/staff_template') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title"><?= esc($page_header) ?></h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('staff/farmers') ?>">Farmers</a></li>
                        <li class="breadcrumb-item active">Details</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Farmer Details</h5>
                        <div>
                            <a href="<?= base_url('staff/farmers/' . $farmer['id'] . '/edit') ?>" class="btn btn-primary">
                                <i class="fas fa-edit"></i> Edit Farmer
                            </a>
                            <a href="<?= base_url('staff/farmers') ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Farmer Photo -->
                        <div class="col-md-3 text-center mb-4">
                            <?php if (!empty($farmer['id_photo'])): ?>
                                <img src="<?= base_url('public/' . $farmer['id_photo']) ?>" 
                                     alt="Farmer Photo" class="img-fluid rounded-circle" 
                                     style="width: 200px; height: 200px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 200px; height: 200px; margin: 0 auto;">
                                    <i class="fas fa-user fa-5x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            <h4 class="mt-3"><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></h4>
                            <p class="text-muted"><?= esc($farmer['farmer_code']) ?></p>
                            <span class="badge bg-<?= $farmer['status'] == 'active' ? 'success' : 'secondary' ?> fs-6">
                                <?= ucfirst(esc($farmer['status'])) ?>
                            </span>
                        </div>

                        <!-- Farmer Details -->
                        <div class="col-md-9">
                            <div class="row">
                                <!-- Personal Information -->
                                <div class="col-12 mb-4">
                                    <h6 class="text-primary border-bottom pb-2">Personal Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Given Name:</strong></td>
                                                    <td><?= esc($farmer['given_name']) ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Surname:</strong></td>
                                                    <td><?= esc($farmer['surname']) ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Date of Birth:</strong></td>
                                                    <td>
                                                        <?= date('M d, Y', strtotime($farmer['date_of_birth'])) ?>
                                                        <small class="text-muted">
                                                            (Age: <?= date_diff(date_create($farmer['date_of_birth']), date_create('today'))->y ?> years)
                                                        </small>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Gender:</strong></td>
                                                    <td>
                                                        <span class="badge bg-<?= $farmer['gender'] == 'Male' ? 'primary' : 'pink' ?>">
                                                            <?= esc($farmer['gender']) ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Marital Status:</strong></td>
                                                    <td><?= esc($farmer['marital_status']) ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Education:</strong></td>
                                                    <td><?= $education ? esc($education['name']) : 'Not specified' ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Course Taken:</strong></td>
                                                    <td><?= esc($farmer['course_taken'] ?? 'Not specified') ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Location Information -->
                                <div class="col-12 mb-4">
                                    <h6 class="text-primary border-bottom pb-2">Location Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Country:</strong></td>
                                                    <td><?= $country ? esc($country['name']) : 'Not specified' ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Province:</strong></td>
                                                    <td><?= $province ? esc($province['name']) : 'Not specified' ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>District:</strong></td>
                                                    <td><?= $district ? esc($district['name']) : 'Not specified' ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>LLG:</strong></td>
                                                    <td><?= $llg ? esc($llg['name']) : 'Not specified' ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Ward:</strong></td>
                                                    <td><?= $ward ? esc($ward['name']) : 'Not specified' ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Village:</strong></td>
                                                    <td><?= esc($farmer['village'] ?? 'Not specified') ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="col-12 mb-4">
                                    <h6 class="text-primary border-bottom pb-2">Contact Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Phone:</strong></td>
                                                    <td><?= esc($farmer['phone'] ?? 'Not provided') ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Email:</strong></td>
                                                    <td><?= esc($farmer['email'] ?? 'Not provided') ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Address:</strong></td>
                                                    <td><?= esc($farmer['address'] ?? 'Not provided') ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- System Information -->
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2">System Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Created:</strong></td>
                                                    <td><?= date('M d, Y H:i', strtotime($farmer['created_at'])) ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Last Updated:</strong></td>
                                                    <td><?= date('M d, Y H:i', strtotime($farmer['updated_at'])) ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
