<?= $this->extend('layouts/staff_layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title"><?= esc($page_header) ?></h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Farmers</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Farmers List</h5>
                        <a href="<?= base_url('staff/farmers/create') ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Farmer
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($farmers)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No farmers found</h5>
                            <p class="text-muted">Start by adding your first farmer.</p>
                            <a href="<?= base_url('staff/farmers/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Farmer
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="farmersTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Farmer Code</th>
                                        <th>Name</th>
                                        <th>Gender</th>
                                        <th>Date of Birth</th>
                                        <th>Location</th>
                                        <th>Phone</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($farmer['farmer_code']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></strong>
                                                </div>
                                                <small class="text-muted"><?= esc($farmer['email'] ?? 'No email') ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $farmer['gender'] == 'Male' ? 'primary' : 'pink' ?>">
                                                    <?= esc($farmer['gender']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?= date('M d, Y', strtotime($farmer['date_of_birth'])) ?>
                                                <br>
                                                <small class="text-muted">
                                                    Age: <?= date_diff(date_create($farmer['date_of_birth']), date_create('today'))->y ?> years
                                                </small>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <?php if (!empty($farmer['ward_name'])): ?>
                                                        <div><strong>Ward:</strong> <?= esc($farmer['ward_name']) ?></div>
                                                    <?php endif; ?>
                                                    <?php if (!empty($farmer['llg_name'])): ?>
                                                        <div><strong>LLG:</strong> <?= esc($farmer['llg_name']) ?></div>
                                                    <?php endif; ?>
                                                    <?php if (!empty($farmer['district_name'])): ?>
                                                        <div><strong>District:</strong> <?= esc($farmer['district_name']) ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td><?= esc($farmer['phone'] ?? 'N/A') ?></td>
                                            <td>
                                                <span class="badge bg-<?= $farmer['status'] == 'active' ? 'success' : 'secondary' ?>">
                                                    <?= ucfirst(esc($farmer['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('staff/farmers/' . $farmer['id']) ?>" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/farmers/' . $farmer['id'] . '/edit') ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/farmers/' . $farmer['id'] . '/delete') ?>" 
                                                       class="btn btn-sm btn-outline-danger" title="Delete"
                                                       onclick="return confirm('Are you sure you want to delete this farmer?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#farmersTable').DataTable({
        responsive: true,
        order: [[0, 'desc']],
        pageLength: 25,
        language: {
            search: "Search farmers:",
            lengthMenu: "Show _MENU_ farmers per page",
            info: "Showing _START_ to _END_ of _TOTAL_ farmers",
            infoEmpty: "No farmers found",
            infoFiltered: "(filtered from _MAX_ total farmers)"
        }
    });
});
</script>
<?= $this->endSection() ?>
