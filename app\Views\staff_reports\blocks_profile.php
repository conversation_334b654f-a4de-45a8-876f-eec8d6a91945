<?= $this->extend('staff/templates/staff_template') ?>

<?php
// Set active menu items
$page_data['active_menu'] = 'reports';
$page_data['active_submenu'] = 'blocks';
?>

<?= $this->section('content') ?>

<?php
// Calculate Crops Statistics
$total_added_plants = 0;
$total_removed_plants = 0;
$total_added_hectares = 0;
$total_removed_hectares = 0;
$dates = [];
$added_trend = [];
$removed_trend = [];

foreach ($crops_data as $crop) {
    if ($crop['action_type'] === 'add') {
        $total_added_plants += $crop['number_of_plants'] ?? 0;
        $total_added_hectares += $crop['hectares'] ?? 0;
    } else {
        $total_removed_plants += $crop['number_of_plants'] ?? 0;
        $total_removed_hectares += $crop['hectares'] ?? 0;
    }

    $date = date('Y-m-d', strtotime($crop['action_date']));
    if (!isset($dates[$date])) {
        $dates[$date] = ['added' => 0, 'removed' => 0];
    }
    if ($crop['action_type'] === 'add') {
        $dates[$date]['added'] += $crop['number_of_plants'] ?? 0;
    } else {
        $dates[$date]['removed'] += $crop['number_of_plants'] ?? 0;
    }
}

ksort($dates);
foreach ($dates as $date => $values) {
    $added_trend[] = $values['added'];
    $removed_trend[] = $values['removed'];
}

$current_plants = $total_added_plants - $total_removed_plants;
$current_hectares = $total_added_hectares - $total_removed_hectares;

// Calculate Disease Statistics
$total_affected_plants = 0;
$total_affected_hectares = 0;
$disease_types = [];
$disease_dates = [];

foreach ($diseases_data as $disease) {
    $total_affected_plants += $disease['number_of_plants'] ?? 0;
    $total_affected_hectares += $disease['hectares'] ?? 0;

    $type = $disease['disease_type'] ?? 'Unknown';
    if (!isset($disease_types[$type])) {
        $disease_types[$type] = 0;
    }
    $disease_types[$type] += $disease['number_of_plants'] ?? 0;

    $date = date('Y-m-d', strtotime($disease['action_date']));
    if (!isset($disease_dates[$date])) {
        $disease_dates[$date] = 0;
    }
    $disease_dates[$date] += $disease['number_of_plants'] ?? 0;
}
ksort($disease_dates);

// Calculate Fertilizer Statistics
$total_fertilizer_quantity = 0;
$fertilizer_types = [];
$fertilizer_dates = [];

foreach ($fertilizer_data as $fertilizer) {
    $total_fertilizer_quantity += $fertilizer['quantity'] ?? 0;

    $type = $fertilizer['fertilizer_name'] ?? 'Unknown';
    if (!isset($fertilizer_types[$type])) {
        $fertilizer_types[$type] = 0;
    }
    $fertilizer_types[$type] += $fertilizer['quantity'] ?? 0;

    $date = date('Y-m-d', strtotime($fertilizer['action_date']));
    if (!isset($fertilizer_dates[$date])) {
        $fertilizer_dates[$date] = 0;
    }
    $fertilizer_dates[$date] += $fertilizer['quantity'] ?? 0;
}
ksort($fertilizer_dates);

// Calculate Pesticide Statistics
$total_pesticide_quantity = 0;
$pesticide_types = [];
$pesticide_dates = [];

foreach ($pesticides_data as $pesticide) {
    $total_pesticide_quantity += $pesticide['quantity'] ?? 0;

    $type = $pesticide['pesticide_name'] ?? 'Unknown';
    if (!isset($pesticide_types[$type])) {
        $pesticide_types[$type] = 0;
    }
    $pesticide_types[$type] += $pesticide['quantity'] ?? 0;

    $date = date('Y-m-d', strtotime($pesticide['action_date']));
    if (!isset($pesticide_dates[$date])) {
        $pesticide_dates[$date] = 0;
    }
    $pesticide_dates[$date] += $pesticide['quantity'] ?? 0;
}
ksort($pesticide_dates);

// Calculate Harvest Statistics
$total_harvest_quantity = 0;
$harvest_types = [];
$harvest_dates = [];

foreach ($harvest_data as $harvest) {
    $total_harvest_quantity += $harvest['quantity'] ?? 0;

    $type = $harvest['item'] ?? 'Unknown';
    if (!isset($harvest_types[$type])) {
        $harvest_types[$type] = 0;
    }
    $harvest_types[$type] += $harvest['quantity'] ?? 0;

    $date = date('Y-m-d', strtotime($harvest['harvest_date']));
    if (!isset($harvest_dates[$date])) {
        $harvest_dates[$date] = 0;
    }
    $harvest_dates[$date] += $harvest['quantity'] ?? 0;
}
ksort($harvest_dates);
?>

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title"><?= $title ?></h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= base_url('staff/reports/blocks') ?>">Farm Blocks</a></li>
                <li class="breadcrumb-item active">Block Profile</li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <!-- Summary Section -->
    <div class="row mb-4">
        <!-- Basic Block Information -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Block Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm table-bordered mb-0">
                                <tr>
                                    <th class="bg-light" width="30%">Block Code:</th>
                                    <td><?= esc($block['block_code']) ?></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Farmer Name:</th>
                                    <td><?= esc($block['given_name']) . ' ' . esc($block['surname']) ?></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Crop:</th>
                                    <td>
                                        <i class="fas fa-circle me-2" style="color: <?= esc($block['crop_color_code']) ?>"></i>
                                        <?= esc($block['crop_name']) ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Status:</th>
                                    <td>
                                        <span class="badge bg-<?= $block['status'] === 'active' ? 'success' : 'danger' ?>">
                                            <?= ucfirst($block['status']) ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm table-bordered mb-0">
                                <tr>
                                    <th class="bg-light" width="30%">LLG:</th>
                                    <td><?= esc($block['llg_name']) ?></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Ward:</th>
                                    <td><?= esc($block['ward_name']) ?></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Village:</th>
                                    <td><?= esc($block['village']) ?></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Block Site:</th>
                                    <td><?= esc($block['block_site']) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards Row -->
        <div class="col-md-12">
            <div class="row">
                <!-- Crops Summary -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">Crops Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered mb-0">
                                            <tr>
                                                <th class="bg-light" width="30%">Total Added:</th>
                                                <td><?= number_format($total_added_plants) ?> plants (<?= number_format($total_added_hectares, 2) ?> ha)</td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Total Removed:</th>
                                                <td><?= number_format($total_removed_plants) ?> plants (<?= number_format($total_removed_hectares, 2) ?> ha)</td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Current Total:</th>
                                                <td class="fw-bold"><?= number_format($current_plants) ?> plants (<?= number_format($current_hectares, 2) ?> ha)</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-center mb-3">Distribution</h6>
                                    <canvas id="plantsPieChart"></canvas>
                                </div>
                                <div class="col-md-12">
                                    <h6 class="text-center mb-3">Trend</h6>
                                    <canvas id="plantsTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Disease Summary -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">Disease/Infection Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered mb-0">
                                            <tr>
                                                <th class="bg-light" width="30%">Total Affected:</th>
                                                <td><?= number_format($total_affected_plants) ?> plants (<?= number_format($total_affected_hectares, 2) ?> ha)</td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Disease Types:</th>
                                                <td><?= count($disease_types) ?> types</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-center mb-3">Distribution by Type</h6>
                                    <canvas id="diseasePieChart"></canvas>
                                </div>
                                <div class="col-md-12">
                                    <h6 class="text-center mb-3">Trend</h6>
                                    <canvas id="diseaseTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fertilizer Summary -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">Fertilizer Usage Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered mb-0">
                                            <tr>
                                                <th class="bg-light" width="30%">Total Usage:</th>
                                                <td><?= number_format($total_fertilizer_quantity) ?> units</td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Types Used:</th>
                                                <td><?= count($fertilizer_types) ?> types</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-center mb-3">Distribution by Type</h6>
                                    <canvas id="fertilizerPieChart"></canvas>
                                </div>
                                <div class="col-md-12">
                                    <h6 class="text-center mb-3">Usage Trend</h6>
                                    <canvas id="fertilizerTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pesticide Summary -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="card-title mb-0">Pesticide Usage Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered mb-0">
                                            <tr>
                                                <th class="bg-light" width="30%">Total Usage:</th>
                                                <td><?= number_format($total_pesticide_quantity) ?> units</td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Types Used:</th>
                                                <td><?= count($pesticide_types) ?> types</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-center mb-3">Distribution by Type</h6>
                                    <canvas id="pesticidePieChart"></canvas>
                                </div>
                                <div class="col-md-12">
                                    <h6 class="text-center mb-3">Usage Trend</h6>
                                    <canvas id="pesticideTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Harvest Summary -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">Harvest Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered mb-0">
                                            <tr>
                                                <th class="bg-light" width="30%">Total Harvest:</th>
                                                <td><?= number_format($total_harvest_quantity) ?> units</td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Harvest Types:</th>
                                                <td><?= count($harvest_types) ?> types</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-center mb-3">Distribution by Type</h6>
                                    <canvas id="harvestPieChart"></canvas>
                                </div>
                                <div class="col-md-12">
                                    <h6 class="text-center mb-3">Harvest Trend</h6>
                                    <canvas id="harvestTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tables Section -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Detailed Reports</h5>
                </div>
                <div class="card-body">
                    <!-- Tabs for different tables -->
                    <style>
                        .nav-tabs .nav-link {
                            font-weight: bold;
                        }

                        .nav-tabs .nav-link.text-success {
                            color: #198754 !important;
                        }

                        .nav-tabs .nav-link.text-warning {
                            color: #856404 !important;
                        }

                        .nav-tabs .nav-link.text-info {
                            color: #0c63e4 !important;
                        }

                        .nav-tabs .nav-link.text-secondary {
                            color: #444444 !important;
                        }

                        .nav-tabs .nav-link:hover {
                            border-color: #e9ecef #e9ecef #dee2e6;
                        }

                        .nav-tabs .nav-link.active.text-success {
                            background-color: #e8f5e9;
                            border-bottom: 2px solid #198754;
                        }

                        .nav-tabs .nav-link.active.text-warning {
                            background-color: #fff8e1;
                            border-bottom: 2px solid #856404;
                        }

                        .nav-tabs .nav-link.active.text-info {
                            background-color: #e3f2fd;
                            border-bottom: 2px solid #0c63e4;
                        }

                        .nav-tabs .nav-link.active.text-secondary {
                            background-color: #f8f9fa;
                            border-bottom: 2px solid #444444;
                        }
                    </style>
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active text-success" data-bs-toggle="tab" href="#cropsTab">Crops Data</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-warning" data-bs-toggle="tab" href="#diseasesTab">Diseases/Infections</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-info" data-bs-toggle="tab" href="#fertilizersTab">Fertilizers</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-secondary" data-bs-toggle="tab" href="#pesticidesTab">Pesticides</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-success" data-bs-toggle="tab" href="#harvestTab">Harvest</a>
                        </li>
                    </ul>

                    <div class="card-header bg-primary text-white mb-3">
                        <h5 class="card-title mb-0">Detailed Reports</h5>
                    </div>

                    <!-- Tab content -->
                    <div class="tab-content pt-3">
                        <!-- Crops Data Table -->
                        <div class="tab-pane fade show active" id="cropsTab">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover w-100">
                                    <thead>
                                        <tr class="bg-light">
                                            <th class="text-center">Action Date</th>
                                            <th class="text-center">Action Type</th>
                                            <th>Action Reason</th>
                                            <th class="text-end">Number of Plants</th>
                                            <th>Breed</th>
                                            <th class="text-end">Hectares</th>
                                            <th>Remarks</th>
                                            <th class="text-center">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($crops_data as $crop): ?>
                                            <tr>
                                                <td class="text-center"><?= date('d-m-Y', strtotime($crop['action_date'])) ?></td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?= $crop['action_type'] === 'add' ? 'success' : 'danger' ?>">
                                                        <?= ucfirst($crop['action_type'] ?? 'N/A') ?>
                                                    </span>
                                                </td>
                                                <td><?= esc($crop['action_reason'] ?? 'N/A') ?></td>
                                                <td class="text-end"><?= number_format($crop['number_of_plants'] ?? 0) ?></td>
                                                <td><?= esc($crop['breed'] ?? 'N/A') ?></td>
                                                <td class="text-end"><?= number_format($crop['hectares'] ?? 0, 2) ?></td>
                                                <td><?= esc($crop['remarks'] ?? '') ?></td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?= $crop['status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= ucfirst($crop['status'] ?? 'N/A') ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Disease/Infection Table -->
                        <div class="tab-pane fade" id="diseasesTab">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover w-100">
                                    <thead>
                                        <tr class="bg-light">
                                            <th class="text-center">Action Date</th>
                                            <th>Disease Type</th>
                                            <th>Disease Name</th>
                                            <th>Description</th>
                                            <th class="text-end">Plants Affected</th>
                                            <th class="text-end">Hectares</th>
                                            <th>Remarks</th>
                                            <th class="text-center">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($diseases_data as $disease): ?>
                                            <tr>
                                                <td class="text-center"><?= date('d-m-Y', strtotime($disease['action_date'])) ?></td>
                                                <td><?= esc($disease['disease_type'] ?? 'N/A') ?></td>
                                                <td><?= esc($disease['disease_name'] ?? 'N/A') ?></td>
                                                <td><?= esc($disease['description'] ?? 'N/A') ?></td>
                                                <td class="text-end"><?= number_format($disease['number_of_plants'] ?? 0) ?></td>
                                                <td class="text-end"><?= number_format($disease['hectares'] ?? 0, 2) ?></td>
                                                <td><?= esc($disease['remarks'] ?? '') ?></td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?= $disease['status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= ucfirst($disease['status'] ?? 'N/A') ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Fertilizer Usage Table -->
                        <div class="tab-pane fade" id="fertilizersTab">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover w-100">
                                    <thead>
                                        <tr class="bg-light">
                                            <th class="text-center">Action Date</th>
                                            <th>Fertilizer</th>
                                            <th>Brand</th>
                                            <th class="text-end">Quantity</th>
                                            <th>Unit</th>
                                            <th>Remarks</th>
                                            <th class="text-center">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($fertilizer_data as $fertilizer): ?>
                                            <tr>
                                                <td class="text-center"><?= date('d-m-Y', strtotime($fertilizer['action_date'])) ?></td>
                                                <td><?= esc($fertilizer['fertilizer_name'] ?? 'N/A') ?></td>
                                                <td><?= esc($fertilizer['brand'] ?? 'N/A') ?></td>
                                                <td class="text-end"><?= number_format($fertilizer['quantity'] ?? 0) ?></td>
                                                <td><?= esc($fertilizer['unit_of_measure'] ?? 'N/A') ?></td>
                                                <td><?= esc($fertilizer['remarks'] ?? '') ?></td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?= $fertilizer['status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= ucfirst($fertilizer['status'] ?? 'N/A') ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Pesticides Usage Table -->
                        <div class="tab-pane fade" id="pesticidesTab">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover w-100">
                                    <thead>
                                        <tr class="bg-light">
                                            <th class="text-center">Action Date</th>
                                            <th>Pesticide</th>
                                            <th>Brand</th>
                                            <th class="text-end">Quantity</th>
                                            <th>Unit</th>
                                            <th>Remarks</th>
                                            <th class="text-center">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pesticides_data as $pesticide): ?>
                                            <tr>
                                                <td class="text-center"><?= date('d-m-Y', strtotime($pesticide['action_date'])) ?></td>
                                                <td><?= esc($pesticide['pesticide_name'] ?? 'N/A') ?></td>
                                                <td><?= esc($pesticide['brand'] ?? 'N/A') ?></td>
                                                <td class="text-end"><?= number_format($pesticide['quantity'] ?? 0) ?></td>
                                                <td><?= esc($pesticide['unit_of_measure'] ?? 'N/A') ?></td>
                                                <td><?= esc($pesticide['remarks'] ?? '') ?></td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?= $pesticide['status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= ucfirst($pesticide['status'] ?? 'N/A') ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Harvest Data Table -->
                        <div class="tab-pane fade" id="harvestTab">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover w-100">
                                    <thead>
                                        <tr class="bg-light">
                                            <th class="text-center">Harvest Date</th>
                                            <th>Item</th>
                                            <th>Description</th>
                                            <th class="text-end">Quantity</th>
                                            <th>Unit</th>
                                            <th>Remarks</th>
                                            <th class="text-center">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($harvest_data as $harvest): ?>
                                            <tr>
                                                <td class="text-center"><?= date('d-m-Y', strtotime($harvest['harvest_date'])) ?></td>
                                                <td><?= esc($harvest['item'] ?? 'N/A') ?></td>
                                                <td><?= esc($harvest['description'] ?? 'N/A') ?></td>
                                                <td class="text-end"><?= number_format($harvest['quantity'] ?? 0) ?></td>
                                                <td><?= esc($harvest['unit_of_measure'] ?? 'N/A') ?></td>
                                                <td><?= esc($harvest['remarks'] ?? '') ?></td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?= $harvest['status'] === 'active' ? 'success' : 'danger' ?>">
                                                        <?= ucfirst($harvest['status'] ?? 'N/A') ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Utility function to generate random colors
    function generateColors(count) {
        const colors = [];
        for (let i = 0; i < count; i++) {
            const hue = (i * 137.508) % 360; // Use golden angle approximation
            colors.push(`hsl(${hue}, 70%, 50%)`);
        }
        return colors;
    }

    // Crops Charts
    const pieCtx = document.getElementById('plantsPieChart').getContext('2d');
    new Chart(pieCtx, {
        type: 'pie',
        data: {
            labels: ['Added', 'Removed'],
            datasets: [{
                data: [<?= $total_added_plants ?>, <?= $total_removed_plants ?>],
                backgroundColor: ['#198754', '#dc3545'],
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Plants Distribution'
                }
            }
        }
    });

    const trendCtx = document.getElementById('plantsTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode(array_keys($dates)) ?>,
            datasets: [{
                label: 'Added',
                data: <?= json_encode($added_trend) ?>,
                borderColor: '#198754',
                tension: 0.1
            }, {
                label: 'Removed',
                data: <?= json_encode($removed_trend) ?>,
                borderColor: '#dc3545',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Plants Trend'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Plants'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            }
        }
    });

    // Disease Charts
    const diseasePieCtx = document.getElementById('diseasePieChart').getContext('2d');
    new Chart(diseasePieCtx, {
        type: 'pie',
        data: {
            labels: <?= json_encode(array_keys($disease_types)) ?>,
            datasets: [{
                data: <?= json_encode(array_values($disease_types)) ?>,
                backgroundColor: generateColors(<?= count($disease_types) ?>),
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Disease Types Distribution'
                }
            }
        }
    });

    const diseaseTrendCtx = document.getElementById('diseaseTrendChart').getContext('2d');
    new Chart(diseaseTrendCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode(array_keys($disease_dates)) ?>,
            datasets: [{
                label: 'Affected Plants',
                data: <?= json_encode(array_values($disease_dates)) ?>,
                borderColor: '#ffc107',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Disease Trend'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Affected Plants'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            }
        }
    });

    // Fertilizer Charts
    const fertilizerPieCtx = document.getElementById('fertilizerPieChart').getContext('2d');
    new Chart(fertilizerPieCtx, {
        type: 'pie',
        data: {
            labels: <?= json_encode(array_keys($fertilizer_types)) ?>,
            datasets: [{
                data: <?= json_encode(array_values($fertilizer_types)) ?>,
                backgroundColor: generateColors(<?= count($fertilizer_types) ?>),
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Fertilizer Types Distribution'
                }
            }
        }
    });

    const fertilizerTrendCtx = document.getElementById('fertilizerTrendChart').getContext('2d');
    new Chart(fertilizerTrendCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode(array_keys($fertilizer_dates)) ?>,
            datasets: [{
                label: 'Usage',
                data: <?= json_encode(array_values($fertilizer_dates)) ?>,
                borderColor: '#0dcaf0',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Fertilizer Usage Trend'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            }
        }
    });

    // Pesticide Charts
    const pesticidePieCtx = document.getElementById('pesticidePieChart').getContext('2d');
    new Chart(pesticidePieCtx, {
        type: 'pie',
        data: {
            labels: <?= json_encode(array_keys($pesticide_types)) ?>,
            datasets: [{
                data: <?= json_encode(array_values($pesticide_types)) ?>,
                backgroundColor: generateColors(<?= count($pesticide_types) ?>),
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Pesticide Types Distribution'
                }
            }
        }
    });

    const pesticideTrendCtx = document.getElementById('pesticideTrendChart').getContext('2d');
    new Chart(pesticideTrendCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode(array_keys($pesticide_dates)) ?>,
            datasets: [{
                label: 'Usage',
                data: <?= json_encode(array_values($pesticide_dates)) ?>,
                borderColor: '#6f42c1',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Pesticide Usage Trend'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            }
        }
    });

    // Harvest Charts
    const harvestPieCtx = document.getElementById('harvestPieChart').getContext('2d');
    new Chart(harvestPieCtx, {
        type: 'pie',
        data: {
            labels: <?= json_encode(array_keys($harvest_types)) ?>,
            datasets: [{
                data: <?= json_encode(array_values($harvest_types)) ?>,
                backgroundColor: generateColors(<?= count($harvest_types) ?>),
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Harvest Types Distribution'
                }
            }
        }
    });

    const harvestTrendCtx = document.getElementById('harvestTrendChart').getContext('2d');
    new Chart(harvestTrendCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode(array_keys($harvest_dates)) ?>,
            datasets: [{
                label: 'Quantity',
                data: <?= json_encode(array_values($harvest_dates)) ?>,
                borderColor: '#fd7e14',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'Harvest Trend'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            }
        }
    });
</script>

<?= $this->endSection() ?>