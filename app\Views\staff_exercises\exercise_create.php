<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Back Button and Title -->
<div class="row mb-4">
    <div class="col-12">
        <a href="<?= base_url('exercises') ?>" class="btn btn-outline-secondary mb-3">
            <i class="fas fa-arrow-left me-2"></i>Back to Exercises
        </a>
        <h2><i class="fas fa-plus-circle me-2"></i>Create New Exercise</h2>
        <p class="text-muted">Create a new field data collection exercise</p>
    </div>
</div>

<!-- Create Exercise Form -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0"><i class="fas fa-clipboard-list me-2"></i>Exercise Details</h5>
    </div>
    <div class="card-body">
        <form action="<?= base_url('exercises/store') ?>" method="post">
            <?= csrf_field() ?>
            
            <div class="row">
                <!-- Basic Details Section -->
                <div class="col-12">
                    <h6 class="border-bottom pb-2 mb-3">Basic Information</h6>
                    
                    <!-- Hidden fields for organization and location from session -->
                    <input type="hidden" name="org_id" value="<?= $selected_org_id ?>">
                    <input type="hidden" name="country_id" value="<?= $selected_country_id ?>">
                    <input type="hidden" name="province_id" value="<?= $selected_province_id ?>">
                    <input type="hidden" name="district_id" value="<?= $selected_district_id ?>">
                    <!-- Hidden status field -->
                    <input type="hidden" name="status" value="draft">
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Exercise Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required 
                               value="<?= old('title') ?>" placeholder="e.g. Q2 Crop Data Collection">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                 placeholder="Brief description of the exercise purpose and objectives"><?= old('description') ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_from" class="form-label">Start Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_from" name="date_from" required 
                                   value="<?= old('date_from') ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date_to" class="form-label">End Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_to" name="date_to" required 
                                   value="<?= old('date_to') ?>">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Officer Assignment Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="border-bottom pb-2 mb-3">Officer Assignment</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="officer_responsible_id" class="form-label">Officer Responsible <span class="text-danger">*</span></label>
                            <select class="form-select" id="officer_responsible_id" name="officer_responsible_id" required>
                                <option value="">Select Responsible Officer</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['id'] ?>" <?= old('officer_responsible_id') == $user['id'] ? 'selected' : '' ?>>
                                        <?= esc($user['name'] ?? $user['username']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Additional Field Officers</label>
                            <select class="form-select" id="officers" name="officers[]" multiple>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['id'] ?>">
                                        <?= esc($user['name'] ?? $user['username']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Hold Ctrl (or Cmd on Mac) to select multiple officers</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 text-end">
                <button type="reset" class="btn btn-secondary me-2">Reset</button>
                <button type="submit" class="btn btn-primary">Create Exercise</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize Select2 for better dropdown experience
    $('#officers').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select officers'
    });

    // Date validation
    $('#date_to').on('change', function() {
        const startDate = new Date($('#date_from').val());
        const endDate = new Date($(this).val());
        
        if (endDate < startDate) {
            toastr.error('End date cannot be before start date');
            $(this).val('');
        }
    });
});
</script>
<?= $this->endSection() ?> 