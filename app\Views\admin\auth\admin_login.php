<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= $title ?> | <?= SYSTEM_NAME ?></title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/dist/css/adminlte.min.css">
    <!-- Toastr -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.css">
</head>
<body class="hold-transition login-page">

<?php if (session()->has('error')): ?>
    <span class="toastrDefaultError"></span>
<?php endif; ?>
<?php if (session()->has('success')): ?>
    <span class="toastrDefaultSuccess"></span>
<?php endif; ?>

<div class="login-box">
    <!-- /.login-logo -->
    <div class="card card-outline card-success">
        <div class="card-header text-center">
            <div class="login-logo">
                <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="System Logo" width="60" height="60">
            </div>
            <h3><b><?= SYSTEM_NAME ?></b></h3>
            <p class="login-box-msg">Admin Portal Login</p>
        </div>
        <div class="card-body">
            <p class="login-box-msg">Sign in to access the admin portal</p>

            <form action="<?= base_url('admin/login') ?>" method="post">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="username" placeholder="System Number or Email" 
                           value="<?= old('username') ?>" required>
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-user"></span>
                        </div>
                    </div>
                </div>
                <div class="input-group mb-3">
                    <input type="password" class="form-control" name="password" placeholder="Password" required>
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-lock"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-8">
                        <div class="icheck-primary">
                            <input type="checkbox" id="remember">
                            <label for="remember">
                                Remember Me
                            </label>
                        </div>
                    </div>
                    <!-- /.col -->
                    <div class="col-4">
                        <button type="submit" class="btn btn-success btn-block">Sign In</button>
                    </div>
                    <!-- /.col -->
                </div>
            </form>

            <div class="social-auth-links text-center mt-2 mb-3">
                <p>- OR -</p>
                <a href="<?= base_url('login') ?>" class="btn btn-block btn-outline-primary">
                    <i class="fas fa-user mr-2"></i> Staff Portal Login
                </a>
                <a href="<?= base_url('dakoii/login') ?>" class="btn btn-block btn-outline-info">
                    <i class="fas fa-cog mr-2"></i> System Admin Login
                </a>
            </div>
            <!-- /.social-auth-links -->

            <p class="mb-1">
                <a href="<?= base_url('forgot-password') ?>">I forgot my password</a>
            </p>
        </div>
        <!-- /.card-body -->
    </div>
    <!-- /.card -->
</div>
<!-- /.login-box -->

<!-- jQuery -->
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/dist/js/adminlte.min.js"></script>
<!-- Toastr -->
<script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.js"></script>

<script>
$(document).ready(function() {
    // Show error messages
    if ($('.toastrDefaultError').length) {
        toastr.error('<?= session()->getFlashdata('error') ?>');
    }
    
    // Show success messages
    if ($('.toastrDefaultSuccess').length) {
        toastr.success('<?= session()->getFlashdata('success') ?>');
    }
    
    // Configure toastr
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": false,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    };
});
</script>

</body>
</html>
