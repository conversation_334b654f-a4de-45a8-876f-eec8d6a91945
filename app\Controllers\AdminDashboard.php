<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UsersModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\PermissionsSetsModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\AdxDistrictModel;

class AdminDashboard extends BaseController
{
    protected $usersModel;
    protected $farmerModel;
    protected $cropBlockModel;
    protected $livestockBlockModel;
    protected $permissionsSetsModel;
    protected $permissionsUserDistrictsModel;
    protected $districtModel;

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropBlockModel = new CropsFarmBlockModel();
        $this->livestockBlockModel = new LivestockFarmBlockModel();
        $this->permissionsSetsModel = new PermissionsSetsModel();
        $this->permissionsUserDistrictsModel = new PermissionsUserDistrictsModel();
        $this->districtModel = new AdxDistrictModel();
        helper(['form', 'url', 'info']);
    }

    /**
     * Admin Dashboard
     */
    public function index()
    {
        $orgId = session()->get('org_id');
        $provinceId = session()->get('orgprovince_id');

        // Get organization statistics
        $stats = [
            'total_users' => $this->usersModel->where('org_id', $orgId)->countAllResults(),
            'active_users' => $this->usersModel->where('org_id', $orgId)->where('status', 1)->countAllResults(),
            'admin_users' => $this->usersModel->where('org_id', $orgId)->where('is_admin', 1)->countAllResults(),
            'field_users' => $this->usersModel->where('org_id', $orgId)->where('role', 'user')->countAllResults(),
            'total_farmers' => $this->farmerModel->where('org_id', $orgId)->countAllResults(),
            'active_farmers' => $this->farmerModel->where('org_id', $orgId)->where('status', 'active')->countAllResults(),
            'total_crop_blocks' => $this->cropBlockModel->where('org_id', $orgId)->countAllResults(),
            'total_livestock_blocks' => $this->livestockBlockModel->where('org_id', $orgId)->countAllResults()
        ];

        // Get recent users (last 10)
        $recentUsers = $this->usersModel->where('org_id', $orgId)
                                       ->orderBy('created_at', 'DESC')
                                       ->limit(10)
                                       ->findAll();

        // Get user distribution by role
        $usersByRole = $this->usersModel->select('role, COUNT(*) as count')
                                       ->where('org_id', $orgId)
                                       ->groupBy('role')
                                       ->findAll();

        // Get district coverage
        $districtCoverage = $this->permissionsUserDistrictsModel
                                ->select('adx_district.name as district_name, COUNT(DISTINCT permissions_user_districts.user_id) as user_count')
                                ->join('adx_district', 'adx_district.id = permissions_user_districts.district_id')
                                ->join('users', 'users.id = permissions_user_districts.user_id')
                                ->where('permissions_user_districts.org_id', $orgId)
                                ->where('users.status', 1)
                                ->groupBy('permissions_user_districts.district_id')
                                ->orderBy('user_count', 'DESC')
                                ->findAll();

        // Get users with most permissions
        $topPermissionUsers = $this->permissionsSetsModel
                                  ->select('users.name, users.role, COUNT(permissions_sets.permission_id) as permission_count')
                                  ->join('users', 'users.id = permissions_sets.user_id')
                                  ->where('users.org_id', $orgId)
                                  ->where('users.status', 1)
                                  ->groupBy('permissions_sets.user_id')
                                  ->orderBy('permission_count', 'DESC')
                                  ->limit(10)
                                  ->findAll();

        // Get monthly user registration trend (last 6 months)
        $monthlyRegistrations = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-$i months"));
            $count = $this->usersModel->where('org_id', $orgId)
                                     ->where('DATE_FORMAT(created_at, "%Y-%m")', $month)
                                     ->countAllResults();
            $monthlyRegistrations[] = [
                'month' => date('M Y', strtotime("-$i months")),
                'count' => $count
            ];
        }

        $data = [
            'title' => 'Admin Dashboard',
            'page_header' => 'Admin Dashboard',
            'page_desc' => 'Organization Management Overview',
            'menu' => 'admin-dashboard',
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'usersByRole' => $usersByRole,
            'districtCoverage' => $districtCoverage,
            'topPermissionUsers' => $topPermissionUsers,
            'monthlyRegistrations' => $monthlyRegistrations
        ];

        return view('admin/dashboard/admin_dashboard', $data);
    }

    /**
     * Get dashboard data via AJAX
     */
    public function getDashboardData()
    {
        $orgId = session()->get('org_id');
        
        $data = [
            'total_users' => $this->usersModel->where('org_id', $orgId)->countAllResults(),
            'active_users' => $this->usersModel->where('org_id', $orgId)->where('status', 1)->countAllResults(),
            'total_farmers' => $this->farmerModel->where('org_id', $orgId)->countAllResults(),
            'total_blocks' => $this->cropBlockModel->where('org_id', $orgId)->countAllResults() + 
                             $this->livestockBlockModel->where('org_id', $orgId)->countAllResults()
        ];

        return $this->response->setJSON($data);
    }

    /**
     * Get user activity data for charts
     */
    public function getUserActivityData()
    {
        $orgId = session()->get('org_id');
        
        // Get user registration trend (last 12 months)
        $registrationTrend = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-$i months"));
            $count = $this->usersModel->where('org_id', $orgId)
                                     ->where('DATE_FORMAT(created_at, "%Y-%m")', $month)
                                     ->countAllResults();
            $registrationTrend[] = [
                'month' => date('M Y', strtotime("-$i months")),
                'count' => $count
            ];
        }

        // Get user status distribution
        $statusDistribution = [
            'active' => $this->usersModel->where('org_id', $orgId)->where('status', 1)->countAllResults(),
            'inactive' => $this->usersModel->where('org_id', $orgId)->where('status', 0)->countAllResults()
        ];

        // Get role distribution
        $roleDistribution = $this->usersModel->select('role, COUNT(*) as count')
                                            ->where('org_id', $orgId)
                                            ->groupBy('role')
                                            ->findAll();

        return $this->response->setJSON([
            'registrationTrend' => $registrationTrend,
            'statusDistribution' => $statusDistribution,
            'roleDistribution' => $roleDistribution
        ]);
    }
}
