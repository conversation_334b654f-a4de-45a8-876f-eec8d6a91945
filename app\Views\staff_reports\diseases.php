<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item">Reports</li>
            <li class="breadcrumb-item active" aria-current="page">Diseases Report</li>
        </ol>
    </nav>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Disease Types Distribution</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="diseaseDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Affected Plants by Disease</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="plantsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Affected Hectares by Disease</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="hectaresChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Disease Distribution by LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Disease Cases Map</h5>
        </div>
        <div class="card-body">
            <div id="diseasesMap" style="height: 500px;"></div>
        </div>
    </div>

    <!-- LLG Distribution Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribution by LLG</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>LLG</th>
                            <?php 
                            // Get unique disease names
                            $unique_diseases = array_unique(array_map(function($item) {
                                return $item['disease_name'];
                            }, $diseases_data));
                            sort($unique_diseases);
                            
                            foreach ($unique_diseases as $disease_name): ?>
                                <th class="text-center" colspan="3"><?= esc($disease_name) ?></th>
                            <?php endforeach; ?>
                            <th class="text-center" colspan="3">Total</th>
                        </tr>
                        <tr>
                            <th></th>
                            <?php foreach ($unique_diseases as $disease_name): ?>
                                <th class="text-center">Cases</th>
                                <th class="text-center">Plants</th>
                                <th class="text-center">Hectares</th>
                            <?php endforeach; ?>
                            <th class="text-center">Cases</th>
                            <th class="text-center">Plants</th>
                            <th class="text-center">Hectares</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Get unique LLGs and sort them
                        $unique_llgs = array_unique(array_map(function($item) {
                            return $item['llg_name'];
                        }, $diseases_data));
                        sort($unique_llgs);

                        // Initialize totals
                        $disease_totals = array_fill_keys($unique_diseases, ['cases' => 0, 'plants' => 0, 'hectares' => 0]);
                        $grand_total = ['cases' => 0, 'plants' => 0, 'hectares' => 0];

                        // Group data by LLG and Disease
                        $llg_data = [];
                        foreach ($diseases_data as $case) {
                            $llg = $case['llg_name'];
                            $disease_name = $case['disease_name'];
                            
                            if (!isset($llg_data[$llg])) {
                                $llg_data[$llg] = array_fill_keys($unique_diseases, ['cases' => 0, 'plants' => 0, 'hectares' => 0]);
                            }
                            
                            $llg_data[$llg][$disease_name]['cases']++;
                            $llg_data[$llg][$disease_name]['plants'] += (int)$case['number_of_plants'];
                            $llg_data[$llg][$disease_name]['hectares'] += (float)$case['hectares'];
                        }

                        // Display data
                        foreach ($unique_llgs as $llg):
                            $llg_total = ['cases' => 0, 'plants' => 0, 'hectares' => 0];
                        ?>
                            <tr>
                                <td><?= esc($llg) ?></td>
                                <?php foreach ($unique_diseases as $disease_name): 
                                    $cases = $llg_data[$llg][$disease_name]['cases'] ?? 0;
                                    $plants = $llg_data[$llg][$disease_name]['plants'] ?? 0;
                                    $hectares = $llg_data[$llg][$disease_name]['hectares'] ?? 0;
                                    
                                    // Update totals
                                    $disease_totals[$disease_name]['cases'] += $cases;
                                    $disease_totals[$disease_name]['plants'] += $plants;
                                    $disease_totals[$disease_name]['hectares'] += $hectares;
                                    $llg_total['cases'] += $cases;
                                    $llg_total['plants'] += $plants;
                                    $llg_total['hectares'] += $hectares;
                                ?>
                                    <td class="text-end"><?= $cases ? number_format($cases) : '-' ?></td>
                                    <td class="text-end"><?= $plants ? number_format($plants) : '-' ?></td>
                                    <td class="text-end"><?= $hectares ? number_format($hectares, 2) : '-' ?></td>
                                <?php endforeach; ?>
                                <td class="text-end fw-bold"><?= number_format($llg_total['cases']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($llg_total['plants']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($llg_total['hectares'], 2) ?></td>
                            </tr>
                        <?php 
                            $grand_total['cases'] += $llg_total['cases'];
                            $grand_total['plants'] += $llg_total['plants'];
                            $grand_total['hectares'] += $llg_total['hectares'];
                        endforeach; 
                        ?>
                        <tr class="table-light">
                            <td class="fw-bold">Total</td>
                            <?php foreach ($unique_diseases as $disease_name): ?>
                                <td class="text-end fw-bold"><?= number_format($disease_totals[$disease_name]['cases']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($disease_totals[$disease_name]['plants']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($disease_totals[$disease_name]['hectares'], 2) ?></td>
                            <?php endforeach; ?>
                            <td class="text-end fw-bold"><?= number_format($grand_total['cases']) ?></td>
                            <td class="text-end fw-bold"><?= number_format($grand_total['plants']) ?></td>
                            <td class="text-end fw-bold"><?= number_format($grand_total['hectares'], 2) ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Table Card -->
    <div class="card">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-bug me-2"></i>Disease Cases Report</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Block Code</th>
                            <th>Farmer Name</th>
                            <th>Crop</th>
                            <th>Disease</th>
                            <th>Type</th>
                            <th>Action Date</th>
                            <th>Plants Affected</th>
                            <th>Hectares</th>
                            <th>Location</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Group data by block and keep only the latest case
                        $distinct_cases = [];
                        foreach ($diseases_data as $case) {
                            $block_id = $case['block_id'];
                            $action_date = strtotime($case['action_date']);
                            
                            // If block not seen before or this case is more recent
                            if (!isset($distinct_cases[$block_id]) || 
                                strtotime($distinct_cases[$block_id]['action_date']) < $action_date) {
                                $distinct_cases[$block_id] = $case;
                            }
                        }

                        // Sort by latest action date
                        uasort($distinct_cases, function($a, $b) {
                            return strtotime($b['action_date']) - strtotime($a['action_date']);
                        });

                        $loop_count = 1;
                        foreach ($distinct_cases as $case): ?>
                            <tr>
                                <td><?= $loop_count++ ?></td>
                                <td><?= esc($case['block_code']) ?></td>
                                <td><?= esc($case['given_name']) . ' ' . esc($case['surname']) ?></td>
                                <td><?= esc($case['crop_name']) ?></td>
                                <td><?= esc($case['disease_name']) ?></td>
                                <td><?= esc($case['disease_type']) ?></td>
                                <td>
                                    <?php 
                                    $action_date = strtotime($case['action_date']);
                                    $is_recent = (time() - $action_date) < (7 * 24 * 60 * 60); // Within last 7 days
                                    ?>
                                    <?= date('d M Y', $action_date) ?>
                                    <?php if ($is_recent): ?>
                                        <span class="badge bg-success ms-1" title="Recent case">New</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end"><?= number_format($case['number_of_plants']) ?></td>
                                <td class="text-end"><?= number_format($case['hectares'], 2) ?></td>
                                <td><?= esc($case['llg_name']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $case['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($case['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

<script>
$(document).ready(function() {
    // Chart data
    const diseasesData = <?= json_encode($diseases_data) ?>;
    
    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 15
                }
            }
        }
    };

    // Process data for charts
    const diseaseStats = diseasesData.reduce((acc, disease) => {
        if (!acc[disease.disease_name]) {
            acc[disease.disease_name] = {
                count: 0,
                plants: 0,
                hectares: 0,
                color: disease.infection_color_code || '#' + Math.floor(Math.random()*16777215).toString(16)
            };
        }
        acc[disease.disease_name].count++;
        acc[disease.disease_name].plants += parseInt(disease.number_of_plants);
        acc[disease.disease_name].hectares += parseFloat(disease.hectares);
        return acc;
    }, {});

    // Process data for LLG distribution
    const llgStats = diseasesData.reduce((acc, disease) => {
        const llgName = disease.llg_name || 'Unknown';
        if (!acc[llgName]) {
            acc[llgName] = {
                diseases: {}
            };
        }
        if (!acc[llgName].diseases[disease.disease_name]) {
            acc[llgName].diseases[disease.disease_name] = {
                count: 0,
                plants: 0,
                hectares: 0,
                color: disease.infection_color_code
            };
        }
        acc[llgName].diseases[disease.disease_name].count++;
        acc[llgName].diseases[disease.disease_name].plants += parseInt(disease.number_of_plants);
        acc[llgName].diseases[disease.disease_name].hectares += parseFloat(disease.hectares);
        return acc;
    }, {});

    // Disease Distribution Chart
    new Chart(document.getElementById('diseaseDistributionChart'), {
        type: 'pie',
        data: {
            labels: Object.keys(diseaseStats),
            datasets: [{
                data: Object.values(diseaseStats).map(stat => stat.count),
                backgroundColor: Object.values(diseaseStats).map(stat => stat.color)
            }]
        },
        options: chartOptions
    });

    // Plants Chart
    new Chart(document.getElementById('plantsChart'), {
        type: 'bar',
        data: {
            labels: Object.keys(diseaseStats),
            datasets: [{
                label: 'Affected Plants',
                data: Object.values(diseaseStats).map(stat => stat.plants),
                backgroundColor: Object.values(diseaseStats).map(stat => stat.color)
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Hectares Chart
    new Chart(document.getElementById('hectaresChart'), {
        type: 'bar',
        data: {
            labels: Object.keys(diseaseStats),
            datasets: [{
                label: 'Affected Hectares',
                data: Object.values(diseaseStats).map(stat => stat.hectares),
                backgroundColor: Object.values(diseaseStats).map(stat => stat.color)
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2);
                        }
                    }
                }
            }
        }
    });

    // LLG Distribution Chart
    const llgLabels = Object.keys(llgStats);
    const diseaseTypes = [...new Set(diseasesData.map(d => d.disease_name))];
    const llgDatasets = diseaseTypes.map(diseaseName => ({
        label: diseaseName,
        data: llgLabels.map(llg => llgStats[llg].diseases[diseaseName]?.hectares || 0),
        backgroundColor: diseasesData.find(d => d.disease_name === diseaseName)?.infection_color_code || '#000000'
    }));

    new Chart(document.getElementById('llgDistributionChart'), {
        type: 'bar',
        data: {
            labels: llgLabels,
            datasets: llgDatasets
        },
        options: {
            ...chartOptions,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2) + ' ha';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y.toFixed(2)} ha`;
                        }
                    }
                }
            }
        }
    });

    // Initialize map
    const map = L.map('diseasesMap').setView([-6.0, 143.0], 6); // Center on PNG

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Add markers for each disease case with GPS coordinates
    const markers = [];
    const distinctCases = {};

    // Group by block to avoid duplicate markers
    diseasesData.forEach(disease => {
        const blockId = disease.block_id;
        if (!distinctCases[blockId] || new Date(disease.action_date) > new Date(distinctCases[blockId].action_date)) {
            distinctCases[blockId] = disease;
        }
    });

    // Create markers for each distinct block
    Object.values(distinctCases).forEach(disease => {
        if (disease.lat && disease.lon) {
            const marker = L.circleMarker([parseFloat(disease.lat), parseFloat(disease.lon)], {
                radius: 6,
                fillColor: disease.crop_color_code || '#000000',
                color: '#fff',
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8
            })
                .bindPopup(`
                    <div class="p-2">
                        <h6 class="mb-2">${disease.block_code}</h6>
                        <table class="table table-sm table-borderless mb-0">
                            <tr>
                                <td><strong>Farmer:</strong></td>
                                <td>${disease.given_name} ${disease.surname}</td>
                            </tr>
                            <tr>
                                <td><strong>Disease:</strong></td>
                                <td>${disease.disease_name}</td>
                            </tr>
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td>${disease.disease_type}</td>
                            </tr>
                            <tr>
                                <td><strong>Plants:</strong></td>
                                <td>${parseInt(disease.number_of_plants).toLocaleString()}</td>
                            </tr>
                            <tr>
                                <td><strong>Hectares:</strong></td>
                                <td>${parseFloat(disease.hectares).toFixed(2)}</td>
                            </tr>
                            <tr>
                                <td><strong>Date:</strong></td>
                                <td>${new Date(disease.action_date).toLocaleDateString()}</td>
                            </tr>
                            <tr>
                                <td><strong>LLG:</strong></td>
                                <td>${disease.llg_name}</td>
                            </tr>
                        </table>
                    </div>
                `);
            markers.push(marker);
        }
    });

    // Create a marker cluster group and add markers
    if (markers.length > 0) {
        const group = L.featureGroup(markers).addTo(map);
        map.fitBounds(group.getBounds());
    } else {
        // If no markers, show a message in the map
        const noDataMessage = L.control({position: 'center'});
        noDataMessage.onAdd = function(map) {
            const div = L.DomUtil.create('div', 'no-data-message');
            div.innerHTML = '<div class="alert alert-info m-3">No GPS coordinates available for the affected farm blocks.</div>';
            return div;
        };
        noDataMessage.addTo(map);
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
