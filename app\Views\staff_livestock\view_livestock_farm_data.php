<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Farm Block Details Card -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="m-0">Farm Block Details</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Block Code:</strong> <?= $block['block_code'] ?></p>
                <p><strong>Farmer Name:</strong> <?= $block['given_name'] . ' ' . $block['surname'] ?></p>
                <p><strong>Province:</strong> <?= $block['province_name'] ?></p>
                <p><strong>District:</strong> <?= $block['district_name'] ?></p>
            </div>
            <div class="col-md-6">
                <p><strong>LLG:</strong> <?= $block['llg_name'] ?></p>
                <p><strong>Ward:</strong> <?= $block['ward_name'] ?></p>
                <p><strong>Village:</strong> <?= $block['village'] ?></p>
                <p><strong>Block Site:</strong> <?= $block['block_site'] ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Livestock Data Card -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="m-0">Livestock Data</h5>
        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addLivestockDataModal">
            <i class="fas fa-plus"></i> Add Livestock Data
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered text-nowrap table-striped" id="livestockDataTable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Livestock Type</th>
                        <th>Breed</th>
                        <th>Male Total</th>
                        <th>Female Total</th>
                        <th>Pasture Type</th>
                        <th>Growth Stage</th>
                        <th>Cost/Head</th>
                        <th>Price Range</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($livestock_data as $data): ?>
                        <tr>
                            <td><?= dateforms(($data['action_date']), 'd-m-Y') ?></td>
                            <td><?= $data['livestock_name'] ?></td>
                            <td><?= $data['breed'] ?></td>
                            <td><?= $data['he_total'] ?></td>
                            <td><?= $data['she_total'] ?></td>
                            <td><?= $data['pasture_type'] ?></td>
                            <td><?= $data['growth_stage'] ?></td>
                            <td><?= $data['cost_per_livestock'] ?></td>
                            <td><?= $data['low_price_per_livestock'] . ' - ' . $data['high_price_per_livestock'] ?></td>
                            <td>
                                <button type="button" class="btn btn-primary btn-sm edit-btn"
                                    data-id="<?= $data['id'] ?>"
                                    data-livestock="<?= $data['livestock_id'] ?>"
                                    data-breed="<?= $data['breed'] ?>"
                                    data-he="<?= $data['he_total'] ?>"
                                    data-she="<?= $data['she_total'] ?>"
                                    data-pasture="<?= $data['pasture_type'] ?>"
                                    data-growth="<?= $data['growth_stage'] ?>"
                                    data-cost="<?= $data['cost_per_livestock'] ?>"
                                    data-low-price="<?= $data['low_price_per_livestock'] ?>"
                                    data-high-price="<?= $data['high_price_per_livestock'] ?>"
                                    data-comments="<?= $data['comments'] ?>"
                                    data-date="<?= date('Y-m-d', strtotime($data['action_date'])) ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <!-- <button type="button" class="btn btn-danger btn-sm delete-btn"
                                    data-id="<?= $data['id'] ?>">
                                    <i class="fas fa-trash"></i>
                                </button> -->
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Livestock Data Modal -->
<div class="modal fade" id="addLivestockDataModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Livestock Data</h5>
                <button type="button" class="close" onclick="$('#addLivestockDataModal').modal('hide')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addLivestockDataForm">
                <div class="modal-body">
                    <input type="hidden" name="block_id" value="<?= $block['id'] ?>">

                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>Livestock Type *</label>
                            <select class="form-control" name="livestock_id" required>
                                <option value="">Select Livestock Type</option>
                                <?php foreach ($livestock_types as $type): ?>
                                    <option value="<?= $type['id'] ?>"><?= $type['livestock_name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group col-md-6">
                            <label>Breed *</label>
                            <input type="text" class="form-control" name="breed" required>
                        </div>

                    </div>

                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>Male Total *</label>
                            <input type="number" class="form-control" name="he_total" min="0" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Female Total *</label>
                            <input type="number" class="form-control" name="she_total" min="0" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>Pasture/Housing Type *</label>
                            <input type="text" class="form-control" name="pasture_type" required>
                            <small class="text-muted">(e.g., open pasture, barn, free-range, coop).</small>
                        </div>

                        <div class="form-group col-md-6">
                            <label>Growth Stage *</label>
                            <input type="text" class="form-control" name="growth_stage" required>
                            <small class="text-muted">(e.g., calf, weaner, yearling, adult).</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-md-4">
                            <label>Cost per Head (PGK) *</label>
                            <input type="number" step="0.01" class="form-control" name="cost_per_livestock" required>
                            <small class="text-muted">Enter the cost per livestock in PGK</small>
                        </div>
                        <div class="form-group col-md-4">
                            <label>Lowest Price (PGK) *</label>
                            <input type="number" step="0.01" class="form-control" name="low_price_per_livestock" required>
                            <small class="text-muted">Minimum selling price</small>
                        </div>
                        <div class="form-group col-md-4">
                            <label>Highest Price (PGK) *</label>
                            <input type="number" step="0.01" class="form-control" name="high_price_per_livestock" required>
                            <small class="text-muted">Maximum selling price</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Comments</label>
                        <textarea class="form-control" name="comments" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label>Reporting Date *</label>
                        <input type="date" class="form-control" name="action_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="$('#addLivestockDataModal').modal('hide')">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Livestock Data Modal -->
<div class="modal fade" id="editLivestockDataModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Livestock Data</h5>
                <button type="button" class="close" onclick="$('#editLivestockDataModal').modal('hide')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editLivestockDataForm">
                <div class="modal-body">
                    <input type="hidden" name="id" id="edit_id">

                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>Livestock Type *</label>
                            <select class="form-control" name="livestock_id" id="edit_livestock_id" required>
                                <option value="">Select Livestock Type</option>
                                <?php foreach ($livestock_types as $type): ?>
                                    <option value="<?= $type['id'] ?>"><?= $type['livestock_name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group col-md-6">
                            <label>Breed *</label>
                            <input type="text" class="form-control" name="breed" id="edit_breed" required>
                        </div>

                    </div>

                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>Male Total *</label>
                            <input type="number" class="form-control" name="he_total" id="edit_he_total" min="0" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Female Total *</label>
                            <input type="number" class="form-control" name="she_total" id="edit_she_total" min="0" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>Pasture/Housing Type *</label>
                            <input type="text" class="form-control" name="pasture_type" id="edit_pasture_type" required>
                            <small class="text-muted">(e.g., open pasture, barn, free-range, coop).</small>
                        </div>

                        <div class="form-group col-md-6">
                            <label>Growth Stage *</label>
                            <input type="text" class="form-control" name="growth_stage" id="edit_growth_stage" required>
                            <small class="text-muted">(e.g., calf, weaner, yearling, adult).</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-md-4">
                            <label>Cost per Head (PGK) *</label>
                            <input type="number" step="0.01" class="form-control" name="cost_per_livestock" id="edit_cost_per_livestock" required>
                            <small class="text-muted">Enter the cost per livestock in PGK</small>
                        </div>
                        <div class="form-group col-md-4">
                            <label>Lowest Price (PGK) *</label>
                            <input type="number" step="0.01" class="form-control" name="low_price_per_livestock" id="edit_low_price_per_livestock" required>
                            <small class="text-muted">Minimum selling price</small>
                        </div>
                        <div class="form-group col-md-4">
                            <label>Highest Price (PGK) *</label>
                            <input type="number" step="0.01" class="form-control" name="high_price_per_livestock" id="edit_high_price_per_livestock" required>
                            <small class="text-muted">Maximum selling price</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Comments</label>
                        <textarea class="form-control" name="comments" id="edit_comments" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label>Reporting Date *</label>
                        <input type="date" class="form-control" name="action_date" id="edit_action_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="$('#editLivestockDataModal').modal('hide')">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#livestockDataTable').DataTable({
            order: [
                [0, 'desc']
            ]
        });

        // Initialize Toastr
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // Add button click handler
        $(document).on('click', '[data-toggle="modal"]', function() {
            var targetModal = $(this).data('target');
            $(targetModal).modal('show');
        });

        // Add Livestock Data
        $('#addLivestockDataForm').on('submit', function(e) {
            e.preventDefault();
            $.ajax({
                url: '<?= base_url('staff/livestock/add-livestock-data') ?>',
                type: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    if (response.status === 'success') {
                        $('#addLivestockDataModal').modal('hide');
                        toastr.success(response.message);
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('An error occurred while adding livestock data');
                }
            });
        });

        // Edit Livestock Data
        $('.edit-btn').on('click', function() {
            var id = $(this).data('id');
            var livestock_id = $(this).data('livestock');
            var action_date = $(this).data('date');
            
            // Set all form values
            $('#edit_id').val(id);
            $('#edit_livestock_id').val(livestock_id);
            $('#edit_breed').val($(this).data('breed'));
            $('#edit_he_total').val($(this).data('he'));
            $('#edit_she_total').val($(this).data('she'));
            $('#edit_pasture_type').val($(this).data('pasture'));
            $('#edit_growth_stage').val($(this).data('growth'));
            $('#edit_cost_per_livestock').val($(this).data('cost'));
            $('#edit_low_price_per_livestock').val($(this).data('low-price'));
            $('#edit_high_price_per_livestock').val($(this).data('high-price'));
            $('#edit_comments').val($(this).data('comments'));
            $('#edit_action_date').val(action_date);

            // Show the modal
            $('#editLivestockDataModal').modal('show');
        });

        // Update Livestock Data
        $('#editLivestockDataForm').on('submit', function(e) {
            e.preventDefault();
            $.ajax({
                url: '<?= base_url('staff/livestock/update-livestock-data') ?>',
                type: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    if (response.status === 'success') {
                        $('#editLivestockDataModal').modal('hide');
                        toastr.success(response.message);
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('An error occurred while updating livestock data');
                }
            });
        });

        // Delete Livestock Data
        $('.delete-btn').on('click', function() {
            if (confirm('Are you sure you want to delete this livestock data?')) {
                var id = $(this).data('id');
                $.ajax({
                    url: '<?= base_url('staff/livestock/delete-livestock-data') ?>',
                    type: 'POST',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            toastr.success(response.message);
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function() {
                        toastr.error('An error occurred while deleting livestock data');
                    }
                });
            }
        });

        // Clear form when modal is closed
        $('#addLivestockDataModal').on('hidden.bs.modal', function() {
            $('#addLivestockDataForm')[0].reset();
        });

        $('#editLivestockDataModal').on('hidden.bs.modal', function() {
            $('#editLivestockDataForm')[0].reset();
        });
    });
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>