<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item active">Admin Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Users</span>
                        <span class="info-box-number"><?= number_format($stats['total_users']) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: <?= $stats['total_users'] > 0 ? ($stats['active_users'] / $stats['total_users']) * 100 : 0 ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?= $stats['active_users'] ?> Active Users
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-user-shield"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Admin Users</span>
                        <span class="info-box-number"><?= number_format($stats['admin_users']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-user-friends"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Field Users</span>
                        <span class="info-box-number"><?= number_format($stats['field_users']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-seedling"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Farmers</span>
                        <span class="info-box-number"><?= number_format($stats['total_farmers']) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: <?= $stats['total_farmers'] > 0 ? ($stats['active_farmers'] / $stats['total_farmers']) * 100 : 0 ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?= $stats['active_farmers'] ?> Active Farmers
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Stats Row -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-primary elevation-1"><i class="fas fa-map"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Crop Farm Blocks</span>
                        <span class="info-box-number"><?= number_format($stats['total_crop_blocks']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-secondary elevation-1"><i class="fas fa-cow"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Livestock Blocks</span>
                        <span class="info-box-number"><?= number_format($stats['total_livestock_blocks']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- User Registration Trend -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line mr-1"></i>
                            User Registration Trend
                        </h3>
                    </div>
                    <div class="card-body">
                        <canvas id="registrationChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                    </div>
                </div>
            </div>

            <!-- User Role Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-1"></i>
                            User Role Distribution
                        </h3>
                    </div>
                    <div class="card-body">
                        <canvas id="roleChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Users -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Users</h3>
                        <div class="card-tools">
                            <a href="<?= base_url('admin/users') ?>" class="btn btn-tool">
                                <i class="fas fa-eye"></i> View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <ul class="users-list clearfix">
                            <?php foreach (array_slice($recentUsers, 0, 8) as $user): ?>
                            <li>
                                <img src="<?= imgcheck($user['id_photo']) ?>" alt="User Image">
                                <a class="users-list-name" href="<?= base_url('admin/users/' . $user['id']) ?>">
                                    <?= esc($user['name']) ?>
                                </a>
                                <span class="users-list-date">
                                    <?= date('M d', strtotime($user['created_at'])) ?>
                                </span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- District Coverage -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">District Coverage</h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($districtCoverage)): ?>
                            <p class="text-muted">No district assignments found.</p>
                        <?php else: ?>
                            <?php foreach (array_slice($districtCoverage, 0, 5) as $district): ?>
                            <div class="progress-group">
                                <?= esc($district['district_name']) ?>
                                <span class="float-right"><b><?= $district['user_count'] ?></b> users</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-primary" style="width: <?= ($district['user_count'] / max(array_column($districtCoverage, 'user_count'))) * 100 ?>%"></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/users/create') ?>" class="btn btn-app">
                                    <i class="fas fa-user-plus"></i> Add User
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/users') ?>" class="btn btn-app">
                                    <i class="fas fa-users"></i> Manage Users
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/reports') ?>" class="btn btn-app">
                                    <i class="fas fa-chart-bar"></i> View Reports
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/settings') ?>" class="btn btn-app">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Registration Trend Chart
    const registrationData = <?= json_encode($monthlyRegistrations) ?>;
    const registrationCtx = document.getElementById('registrationChart').getContext('2d');
    new Chart(registrationCtx, {
        type: 'line',
        data: {
            labels: registrationData.map(item => item.month),
            datasets: [{
                label: 'New Users',
                data: registrationData.map(item => item.count),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Role Distribution Chart
    const roleData = <?= json_encode($usersByRole) ?>;
    const roleCtx = document.getElementById('roleChart').getContext('2d');
    new Chart(roleCtx, {
        type: 'doughnut',
        data: {
            labels: roleData.map(item => item.role.charAt(0).toUpperCase() + item.role.slice(1)),
            datasets: [{
                data: roleData.map(item => item.count),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>

<?= $this->endSection() ?>
